<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recording_page"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai"
    android:fitsSystemWindows="true"
    android:padding="15dp"
    tools:context=".RecordingPageActivity">

    <ImageView
        android:id="@+id/recording_page_close_page"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:contentDescription="@string/recording_page_desc_关闭页面"
        android:padding="15dp"
        android:src="@drawable/icon_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/recording_page_InOutTrans_tabs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/YinBai"
        android:contentDescription="@string/recording_page_desc_收支转账导航栏"
        app:layout_constraintBottom_toBottomOf="@id/recording_page_close_page"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tabBackground="@null"
        app:tabRippleColor="@null" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/recording_page_InOutTrans_views"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recording_page_close_page"
        app:layout_constraintBottom_toTopOf="@id/recording_page_num_keyboard"/>

    <LinearLayout
        android:id="@+id/recording_page_num_keyboard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <include layout="@layout/widget_num_keyboard" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>