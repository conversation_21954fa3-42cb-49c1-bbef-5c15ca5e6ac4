package com.example.likeqianwang.ui.recording_in;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.TransactionCategoryViewModel;
import com.example.likeqianwang.adapters.RecordingDialogSubcategory_adapter;
import com.example.likeqianwang.adapters.RecordingInOutCategory_adapter;
import com.example.likeqianwang.databinding.ItemRecordingInOutCategoryListViewBinding;
import com.google.android.material.bottomsheet.BottomSheetDialog;

public class RecordingInFragment extends Fragment implements RecordingDataProvider {
    private ItemRecordingInOutCategoryListViewBinding recordingInOutCategoryListViewBinding;
    private RecordingInOutCategory_adapter categoryAdapter;
    private TransactionCategoryViewModel viewModel;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        recordingInOutCategoryListViewBinding = ItemRecordingInOutCategoryListViewBinding.inflate(inflater, container, false);
        return recordingInOutCategoryListViewBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化ViewModel
        viewModel = new ViewModelProvider(requireActivity()).get(TransactionCategoryViewModel.class);

        // 观察收入分类数据
        viewModel.getIncomeCategories().observe(getViewLifecycleOwner(), categories -> {
            if (categories != null && !categories.isEmpty()) {
                recordingInOutCategoryListViewBinding.rvRecordingInOutCategoryList.setLayoutManager(new GridLayoutManager(requireContext(), 5));
                categoryAdapter = new RecordingInOutCategory_adapter(requireContext(), categories, RecordingInOutCategory_adapter.TYPE_INCOME);
                recordingInOutCategoryListViewBinding.rvRecordingInOutCategoryList.setAdapter(categoryAdapter);

                // 重新设置点击监听器
                categoryAdapter.setOnCategoryClickListener(new RecordingInOutCategory_adapter.OnCategoryClickListener() {
                    @Override
                    public void onCategoryClick(TransactionCategory category, int position) {
                        // 如果该分类有子分类，则显示子分类选择对话框
                        if (category.isHasSubCategories() && !category.getSubcategories().isEmpty()) {
                            showSubcategoryDialog(category, position);
                        }
                    }

                    @Override
                    public void onManagementClick() {
                        // 跳转到分类管理页面
                        // 这里可以添加跳转逻辑
                    }
                });
            }
        });
    }

    // 显示子分类选择对话框
    private void showSubcategoryDialog(TransactionCategory category, int position) {
        BottomSheetDialog dialog = new BottomSheetDialog(requireContext());
        dialog.setContentView(R.layout.dialog_recording_subcategory_list_view);

        TextView titleTextView = dialog.findViewById(R.id.tv_recording_subcategory_title);
        RecyclerView subcategoryRecyclerView = dialog.findViewById(R.id.rv_recording_subcategory_list);
        ImageView closeButton = dialog.findViewById(R.id.iv_recording_subcategory_close);

        titleTextView.setText(category.getCategoryName());

        // 设置子分类适配器
        RecordingDialogSubcategory_adapter subcategoryAdapter =
                new RecordingDialogSubcategory_adapter(requireContext(), category.getSubcategories());
        subcategoryRecyclerView.setLayoutManager(new GridLayoutManager(requireContext(), 3));
        subcategoryRecyclerView.setAdapter(subcategoryAdapter);

        // 设置子分类点击监听器
        subcategoryAdapter.setOnSubcategoryClickListener((subcategory, subPosition) -> {
            // 子分类被点击时的处理
            // 更新主分类适配器中显示的子分类
            categoryAdapter.setSelectedSubcategory(subcategory, position);
            dialog.dismiss();
        });

        // 设置取消按钮点击事件
        closeButton.setOnClickListener(v -> dialog.dismiss());

        dialog.show();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        recordingInOutCategoryListViewBinding = null;
    }

    // 实现RecordingDataProvider接口
    @Override
    public TransactionCategory getSelectedCategory() {
        return categoryAdapter != null ? categoryAdapter.getSelectedCategory() : null;
    }

    @Override
    public TransactionSubcategory getSelectedSubcategory() {
        return categoryAdapter != null ? categoryAdapter.getSelectedSubcategory() : null;
    }

    @Override
    public Account getFromAccount() {
        return null; // 收入页面不需要转账账户
    }

    @Override
    public Account getToAccount() {
        return null; // 收入页面不需要转账账户
    }

    @Override
    public String getTransferType() {
        return null; // 收入页面不需要转账类型
    }

    @Override
    public boolean hasRequiredData() {
        return getSelectedCategory() != null;
    }
}
