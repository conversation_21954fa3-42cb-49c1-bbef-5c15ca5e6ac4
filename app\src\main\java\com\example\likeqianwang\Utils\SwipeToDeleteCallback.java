package com.example.likeqianwang.Utils;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.R;
import com.example.likeqianwang.adapters.AccountItemInWallet_adapter;

public class SwipeToDeleteCallback extends ItemTouchHelper.SimpleCallback{
    private final SwipeActionListener swipeActionListener;
    private final ColorDrawable background;
    private final Context context;
    private final boolean isNestedItem;
    private static final float buttonWidth = 200; // 删除按钮宽度
    private RecyclerView.ViewHolder currentViewHolder = null;
    private RecyclerView.OnItemTouchListener touchListener = null;
    private RecyclerView touchListenerAttachedTo = null; // 跟踪已附加监听器的RecyclerView
    private boolean isItemSwiped = false;
    private final Paint textPaint;

    public interface SwipeActionListener {
        void onDeleteClick(RecyclerView.ViewHolder viewHolder);
    }

    public SwipeToDeleteCallback(Context context, SwipeActionListener listener, boolean isNestedItem) {
        super(0, ItemTouchHelper.LEFT); // 只支持左滑（显示在右侧的删除按钮）
        this.swipeActionListener = listener;
        this.context = context;
        this.isNestedItem = isNestedItem;

        int backgroundColor = ContextCompat.getColor(context, R.color.delete_red);
        background = new ColorDrawable(backgroundColor);

        // 初始化文本画笔
        textPaint = new Paint();
        textPaint.setColor(Color.WHITE);
        textPaint.setTextSize(48);
        textPaint.setAntiAlias(true);
        textPaint.setTextAlign(Paint.Align.CENTER);

        // 创建触摸监听器
        createTouchListener();
    }

    private void createTouchListener() {
        touchListener = new RecyclerView.OnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                if (currentViewHolder == null) return false;

                View itemView = currentViewHolder.itemView;
                if (!isItemSwiped) return false;

                // 检查是否点击了删除按钮区域
                if (e.getAction() == MotionEvent.ACTION_UP) {
                    // 获取触摸点的绝对坐标
                    float touchX = e.getRawX();
                    float touchY = e.getRawY();

                    // 获取删除按钮的绝对位置
                    int[] itemViewLocation = new int[2];
                    itemView.getLocationOnScreen(itemViewLocation);

                    // 计算删除按钮的绝对区域
                    float buttonLeft = itemViewLocation[0] + itemView.getWidth() - buttonWidth;
                    float buttonRight = itemViewLocation[0] + itemView.getWidth();
                    float buttonTop = itemViewLocation[1];
                    float buttonBottom = itemViewLocation[1] + itemView.getHeight();

                    // 检查是否点击了删除按钮区域
                    if (touchX >= buttonLeft && touchX <= buttonRight &&
                            touchY >= buttonTop && touchY <= buttonBottom) {
                        // 触发删除点击事件
                        swipeActionListener.onDeleteClick(currentViewHolder);
                        return true;
                    } else {
                        // 点击了其他区域，恢复原状
                        resetView(currentViewHolder);
                        return true;
                    }
                }
                return false;
            }

            @Override
            public void onTouchEvent(@NonNull RecyclerView rv, @NonNull MotionEvent e) {
                // 不需要实现
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
                // 不需要实现
            }
        };
    }

    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
        return false;
    }

    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
    }

    @Override
    public void onChildDraw(@NonNull Canvas c, @NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, float dX, float dY, int actionState, boolean isCurrentlyActive) {
        if (actionState == ItemTouchHelper.ACTION_STATE_SWIPE) {
            // 确保滑动不超过删除按钮宽度
            float swipeThreshold = -buttonWidth;

            // 限制最大滑动距离
            if (dX < swipeThreshold) {
                dX = swipeThreshold;
            }

            // 如果是新的ViewHolder，关闭之前打开的项目
            if (currentViewHolder != null && currentViewHolder != viewHolder) {
                resetView(currentViewHolder);
            }

            // 更新当前ViewHolder
            currentViewHolder = viewHolder;

            // 绘制背景
            View itemView = viewHolder.itemView;
            int itemHeight = itemView.getHeight();
            int itemWidth = itemView.getWidth();

            // 设置背景颜色
            background.setBounds(
                    itemView.getRight() + (int) dX,
                    itemView.getTop(),
                    itemView.getRight(),
                    itemView.getBottom()
            );
            background.draw(c);

            // 绘制删除文本
            String deleteText = "删除";
            float textX = itemView.getRight() - buttonWidth / 2;
            float textY = itemView.getTop() + (itemHeight / 2) + (textPaint.getTextSize() / 3);
            c.drawText(deleteText, textX, textY, textPaint);

            // 设置滑动状态
            isItemSwiped = dX < -20; // 只要有一点滑动就认为是滑动状态

            // 确保触摸监听器已附加到RecyclerView
            if (touchListenerAttachedTo != recyclerView) {
                if (touchListenerAttachedTo != null) {
                    touchListenerAttachedTo.removeOnItemTouchListener(touchListener);
                }
                recyclerView.addOnItemTouchListener(touchListener);
                touchListenerAttachedTo = recyclerView;
            }

            // 如果是嵌套项目，更新适配器的打开位置
            if (isNestedItem && recyclerView.getAdapter() instanceof AccountItemInWallet_adapter adapter) {
                if (isCurrentlyActive) {
                    if (dX < -20) {
                        adapter.setOpenPosition(viewHolder.getAdapterPosition());
                    } else {
                        if (adapter.getOpenPosition() == viewHolder.getAdapterPosition()) {
                            adapter.setOpenPosition(RecyclerView.NO_POSITION);
                        }
                    }
                }
            }
        }

        super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive);
    }

    // 用于手动重置项目状态
    public void resetView(RecyclerView.ViewHolder viewHolder) {
        if (viewHolder != null) {
            viewHolder.itemView.animate().translationX(0).setDuration(200).start();
            isItemSwiped = false;

            // 如果是嵌套项目，更新适配器的打开位置
            if (isNestedItem && viewHolder.itemView.getParent() instanceof RecyclerView parentRecyclerView) {
                if (parentRecyclerView.getAdapter() instanceof AccountItemInWallet_adapter adapter) {
                    if (adapter.getOpenPosition() == viewHolder.getAdapterPosition()) {
                        adapter.setOpenPosition(RecyclerView.NO_POSITION);
                    }
                }
            }
        }
    }

    // 清理资源
    public void cleanup(RecyclerView recyclerView) {
        if (recyclerView != null && touchListener != null) {
            recyclerView.removeOnItemTouchListener(touchListener);
            if (touchListenerAttachedTo == recyclerView) {
                touchListenerAttachedTo = null;
            }
        }
    }
}
