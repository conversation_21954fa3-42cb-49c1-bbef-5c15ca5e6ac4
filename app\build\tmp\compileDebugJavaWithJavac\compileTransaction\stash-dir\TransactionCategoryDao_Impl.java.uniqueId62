package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.TransactionCategory;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TransactionCategoryDao_Impl implements TransactionCategoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TransactionCategory> __insertionAdapterOfTransactionCategory;

  private final EntityDeletionOrUpdateAdapter<TransactionCategory> __deletionAdapterOfTransactionCategory;

  private final EntityDeletionOrUpdateAdapter<TransactionCategory> __updateAdapterOfTransactionCategory;

  public TransactionCategoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTransactionCategory = new EntityInsertionAdapter<TransactionCategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `transaction_categories` (`categoryId`,`categoryName`,`categoryIcon`,`categoryType`,`hasSubCategories`,`orderIndex`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionCategory entity) {
        statement.bindLong(1, entity.getCategoryId());
        if (entity.getCategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategoryName());
        }
        statement.bindLong(3, entity.getCategoryIcon());
        statement.bindLong(4, entity.getCategoryType());
        final int _tmp = entity.isHasSubCategories() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getOrderIndex());
      }
    };
    this.__deletionAdapterOfTransactionCategory = new EntityDeletionOrUpdateAdapter<TransactionCategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `transaction_categories` WHERE `categoryId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionCategory entity) {
        statement.bindLong(1, entity.getCategoryId());
      }
    };
    this.__updateAdapterOfTransactionCategory = new EntityDeletionOrUpdateAdapter<TransactionCategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `transaction_categories` SET `categoryId` = ?,`categoryName` = ?,`categoryIcon` = ?,`categoryType` = ?,`hasSubCategories` = ?,`orderIndex` = ? WHERE `categoryId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionCategory entity) {
        statement.bindLong(1, entity.getCategoryId());
        if (entity.getCategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getCategoryName());
        }
        statement.bindLong(3, entity.getCategoryIcon());
        statement.bindLong(4, entity.getCategoryType());
        final int _tmp = entity.isHasSubCategories() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getOrderIndex());
        statement.bindLong(7, entity.getCategoryId());
      }
    };
  }

  @Override
  public long insert(final TransactionCategory transactionCategory) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfTransactionCategory.insertAndReturnId(transactionCategory);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final TransactionCategory transactionCategory) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfTransactionCategory.handle(transactionCategory);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final TransactionCategory transactionCategory) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfTransactionCategory.handle(transactionCategory);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public LiveData<List<TransactionCategory>> getCategoriesByType(final int type) {
    final String _sql = "SELECT * FROM transaction_categories WHERE categoryType = ? ORDER BY orderIndex ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, type);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_categories"}, false, new Callable<List<TransactionCategory>>() {
      @Override
      @Nullable
      public List<TransactionCategory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
          final int _cursorIndexOfCategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryIcon");
          final int _cursorIndexOfCategoryType = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryType");
          final int _cursorIndexOfHasSubCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "hasSubCategories");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
          final List<TransactionCategory> _result = new ArrayList<TransactionCategory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TransactionCategory _item;
            final String _tmpCategoryName;
            if (_cursor.isNull(_cursorIndexOfCategoryName)) {
              _tmpCategoryName = null;
            } else {
              _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
            }
            final int _tmpCategoryIcon;
            _tmpCategoryIcon = _cursor.getInt(_cursorIndexOfCategoryIcon);
            final int _tmpCategoryType;
            _tmpCategoryType = _cursor.getInt(_cursorIndexOfCategoryType);
            final boolean _tmpHasSubCategories;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfHasSubCategories);
            _tmpHasSubCategories = _tmp != 0;
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            _item = new TransactionCategory(_tmpCategoryName,_tmpCategoryIcon,_tmpCategoryType,_tmpHasSubCategories,_tmpOrderIndex);
            final long _tmpCategoryId;
            _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            _item.setCategoryId(_tmpCategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<TransactionCategory> getCategoriesByTypeSync(final int type) {
    final String _sql = "SELECT * FROM transaction_categories WHERE categoryType = ? ORDER BY orderIndex ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, type);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
      final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
      final int _cursorIndexOfCategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryIcon");
      final int _cursorIndexOfCategoryType = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryType");
      final int _cursorIndexOfHasSubCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "hasSubCategories");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
      final List<TransactionCategory> _result = new ArrayList<TransactionCategory>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final TransactionCategory _item;
        final String _tmpCategoryName;
        if (_cursor.isNull(_cursorIndexOfCategoryName)) {
          _tmpCategoryName = null;
        } else {
          _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
        }
        final int _tmpCategoryIcon;
        _tmpCategoryIcon = _cursor.getInt(_cursorIndexOfCategoryIcon);
        final int _tmpCategoryType;
        _tmpCategoryType = _cursor.getInt(_cursorIndexOfCategoryType);
        final boolean _tmpHasSubCategories;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfHasSubCategories);
        _tmpHasSubCategories = _tmp != 0;
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _item = new TransactionCategory(_tmpCategoryName,_tmpCategoryIcon,_tmpCategoryType,_tmpHasSubCategories,_tmpOrderIndex);
        final long _tmpCategoryId;
        _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        _item.setCategoryId(_tmpCategoryId);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getCategoriesCount() {
    final String _sql = "SELECT COUNT(*) FROM transaction_categories";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public TransactionCategory getCategoryById(final long categoryId) {
    final String _sql = "SELECT * FROM transaction_categories WHERE categoryId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, categoryId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
      final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
      final int _cursorIndexOfCategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryIcon");
      final int _cursorIndexOfCategoryType = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryType");
      final int _cursorIndexOfHasSubCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "hasSubCategories");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
      final TransactionCategory _result;
      if (_cursor.moveToFirst()) {
        final String _tmpCategoryName;
        if (_cursor.isNull(_cursorIndexOfCategoryName)) {
          _tmpCategoryName = null;
        } else {
          _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
        }
        final int _tmpCategoryIcon;
        _tmpCategoryIcon = _cursor.getInt(_cursorIndexOfCategoryIcon);
        final int _tmpCategoryType;
        _tmpCategoryType = _cursor.getInt(_cursorIndexOfCategoryType);
        final boolean _tmpHasSubCategories;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfHasSubCategories);
        _tmpHasSubCategories = _tmp != 0;
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _result = new TransactionCategory(_tmpCategoryName,_tmpCategoryIcon,_tmpCategoryType,_tmpHasSubCategories,_tmpOrderIndex);
        final long _tmpCategoryId;
        _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        _result.setCategoryId(_tmpCategoryId);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public TransactionCategory getCategoryByIdSync(final long categoryId) {
    final String _sql = "SELECT * FROM transaction_categories WHERE categoryId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, categoryId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
      final int _cursorIndexOfCategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryName");
      final int _cursorIndexOfCategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryIcon");
      final int _cursorIndexOfCategoryType = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryType");
      final int _cursorIndexOfHasSubCategories = CursorUtil.getColumnIndexOrThrow(_cursor, "hasSubCategories");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
      final TransactionCategory _result;
      if (_cursor.moveToFirst()) {
        final String _tmpCategoryName;
        if (_cursor.isNull(_cursorIndexOfCategoryName)) {
          _tmpCategoryName = null;
        } else {
          _tmpCategoryName = _cursor.getString(_cursorIndexOfCategoryName);
        }
        final int _tmpCategoryIcon;
        _tmpCategoryIcon = _cursor.getInt(_cursorIndexOfCategoryIcon);
        final int _tmpCategoryType;
        _tmpCategoryType = _cursor.getInt(_cursorIndexOfCategoryType);
        final boolean _tmpHasSubCategories;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfHasSubCategories);
        _tmpHasSubCategories = _tmp != 0;
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _result = new TransactionCategory(_tmpCategoryName,_tmpCategoryIcon,_tmpCategoryType,_tmpHasSubCategories,_tmpOrderIndex);
        final long _tmpCategoryId;
        _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        _result.setCategoryId(_tmpCategoryId);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
