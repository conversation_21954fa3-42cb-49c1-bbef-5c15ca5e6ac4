package com.example.likeqianwang.ui.recording_transer;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Outline;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.AlignmentSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.ViewParent;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.R;
import com.example.likeqianwang.databinding.ItemRecordingTransferViewBinding;
import com.example.likeqianwang.ui.RecordingTransferAccountSelectionDialog;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.ShapeAppearanceModel;

import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class RecordingTransferFragment extends Fragment {
    private ItemRecordingTransferViewBinding recordingTransferViewBinding;

    // 账户相关变量
    private Account fromAccount;
    private Account toAccount;
    private String transferType = "转账"; // 默认为普通转账

    // 账户选择对话框类型标识
    private static final int FROM_ACCOUNT = 1;
    private static final int TO_ACCOUNT = 2;
    private int currentSelectionType;

    // 数据库访问
    private AccountDao accountDao;
    private List<Account> allAccounts;
    private ExecutorService executorService;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        recordingTransferViewBinding = ItemRecordingTransferViewBinding.inflate(inflater, container, false);
        return recordingTransferViewBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 初始化数据库访问
        accountDao = AppDatabase.getInstance(requireContext()).accountDao();
        executorService = Executors.newSingleThreadExecutor();

        // 加载所有账户
        loadAccounts();

        View layoutFromAccount = recordingTransferViewBinding.recordingPageTransferFromAccount; // 获取您的ConstraintLayout实例
        layoutFromAccount.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                int width = view.getWidth();
                int height = view.getHeight();
                float cornerRadius = getResources().getDimension(R.dimen.recording_transfer_account_icon_circle_radius);

                Path path = new Path();

                // 从左下角开始，顺时针绘制
                path.moveTo(0, height); // 左下角 (直角)
                path.lineTo(0, cornerRadius); // 移动到左上圆角开始处

                // 左上圆角
                // rectF 定义了圆角所在的椭圆的边界
                RectF topLeftRect = new RectF(0, 0, cornerRadius * 2, cornerRadius * 2);
                path.arcTo(topLeftRect, 180, 90, false); // 从180度开始，扫过90度

                path.lineTo(width - cornerRadius, 0); // 移动到右上圆角开始处

                // 右上圆角
                RectF topRightRect = new RectF(width - (cornerRadius * 2), 0, width, cornerRadius * 2);
                path.arcTo(topRightRect, 270, 90, false); // 从270度开始，扫过90度

                path.lineTo(width, height); // 移动到右下角 (直角)
                path.close(); // 关闭路径，回到起点 (左下角)

                outline.setPath(path);
            }
        });

        View layoutToAccount = recordingTransferViewBinding.recordingPageTransferToAccount; // 获取您的ConstraintLayout实例
        layoutToAccount.setOutlineProvider(new ViewOutlineProvider() {
            @Override
            public void getOutline(View view, Outline outline) {
                int width = view.getWidth();
                int height = view.getHeight();
                float cornerRadius = getResources().getDimension(R.dimen.recording_transfer_account_icon_circle_radius);

                Path path = new Path();

                // 从左上角开始，顺时针绘制
                path.moveTo(0, 0); // 左上角 (直角)
                path.lineTo(width, 0); // 右上角 (直角)
                path.lineTo(width, height - cornerRadius); // 移动到右下圆角开始处

                // 右下圆角
                // rectF 定义了圆角所在的椭圆的边界
                RectF bottomRightRect = new RectF(width - (cornerRadius * 2), height - (cornerRadius * 2), width, height);
                path.arcTo(bottomRightRect, 0, 90, false); // 从0度开始，扫过90度

                path.lineTo(cornerRadius, height); // 移动到左下圆角开始处

                // 左下圆角
                RectF bottomLeftRect = new RectF(0, height - (cornerRadius * 2), cornerRadius * 2, height);
                path.arcTo(bottomLeftRect, 90, 90, false); // 从90度开始，扫过90度

                path.close(); // 关闭路径，回到起点 (左上角)

                outline.setPath(path);
            }
        });

        // 设置转出账户按钮点击事件
        recordingTransferViewBinding.recordingPageTransferFromAccount.setOnClickListener(v -> {
            currentSelectionType = FROM_ACCOUNT;
            showAccountSelectionDialog("选择转出账户");
        });

        // 设置转入账户按钮点击事件
        recordingTransferViewBinding.recordingPageTransferToAccount.setOnClickListener(v -> {
            currentSelectionType = TO_ACCOUNT;
            showAccountSelectionDialog("选择转入账户");
        });

        // 设置交换按钮点击事件
        recordingTransferViewBinding.btnRecordingPageTransferSwapAccounts.setOnClickListener(v -> swapAccounts());
    }

    /**
     * 加载所有账户数据
     */
    private void loadAccounts() {
        executorService.execute(() -> {
            allAccounts = accountDao.getAllAccountsSync();
            // 确保在主线程更新UI
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    // 账户加载完成后的操作（如果需要）
                });
            }
        });
    }

    /**
     * 显示账户选择底部对话框
     */
    private void showAccountSelectionDialog(String title) {
        if (allAccounts == null || allAccounts.isEmpty()) {
            Toast.makeText(requireContext(), "没有可用账户", Toast.LENGTH_SHORT).show();
            return;
        }

        Account existedAccount = currentSelectionType == FROM_ACCOUNT ? toAccount : fromAccount;

        RecordingTransferAccountSelectionDialog transferAccountSelectionDialog = RecordingTransferAccountSelectionDialog.newInstance(
                title,
                allAccounts,
                account -> {
                    if (existedAccount != null && existedAccount.getAccountId() == account.getAccountId()) {
                        Toast.makeText(requireContext(), "不能选择相同的账户", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (currentSelectionType == FROM_ACCOUNT) {
                        setFromAccount(account);
                    } else {
                        setToAccount(account);
                    }
                    updateTransferType();
                }
        );
        transferAccountSelectionDialog.show(getChildFragmentManager(), "AccountSelection");
    }

    /**
     * 设置转出账户
     */
    private void setFromAccount(Account account) {
        fromAccount = account;

        // 更新转出账户按钮显示
        if (account != null) {
            // 设置账户名称
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountName.setText(account.getAccountName());

            // 创建并设置圆形图标
            ShapeableImageView iconView = recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon;
            if (account.getBankIcon() != 0) {
                iconView.setImageResource(account.getBankIcon());
            } else {
                iconView.setImageResource(account.getAccountTypeIcon());
            }
            iconView.setVisibility(View.VISIBLE);

            // 设置圆形样式
            ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel()
                    .toBuilder()
                    .setAllCorners(CornerFamily.ROUNDED, getResources().getDimension(R.dimen.recording_transfer_account_icon_circle_radius))
                    .build();
            iconView.setShapeAppearanceModel(shapeAppearanceModel);

            // 设置边框
            iconView.setStrokeWidth(1);
            iconView.setStrokeColor(ColorStateList.valueOf(getResources().getColor(R.color.YinBai)));

            // 设置图标大小和内边距
            int padding = (int) getResources().getDimension(R.dimen.recording_transfer_account_icon_padding);

            // 应用内边距
            iconView.setPadding(padding, padding, padding, padding);

            // 设置账户余额（添加到按钮文本后面）
            String balanceText = String.format(Locale.CHINA,"¥%.2f", account.getAccountBalance());
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setText(balanceText);
        }
    }

    /**
     * 设置转入账户
     */
    private void setToAccount(Account account) {
        toAccount = account;

        // 更新转入账户按钮显示
        if (account != null) {
            // 设置账户名称
            recordingTransferViewBinding.tvRecordingPageTransferToAccountName.setText(account.getAccountName());

            // 创建并设置圆形图标
            ShapeableImageView iconView = recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon;
            if (account.getBankIcon() != 0) {
                iconView.setImageResource(account.getBankIcon());
            } else {
                iconView.setImageResource(account.getAccountTypeIcon());
            }
            iconView.setVisibility(View.VISIBLE);

            // 设置圆形样式
            ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel()
                    .toBuilder()
                    .setAllCorners(CornerFamily.ROUNDED, getResources().getDimension(R.dimen.recording_transfer_account_icon_circle_radius))
                    .build();
            iconView.setShapeAppearanceModel(shapeAppearanceModel);

            // 设置边框
            iconView.setStrokeWidth(1);
            iconView.setStrokeColor(ColorStateList.valueOf(getResources().getColor(R.color.YinBai)));

            // 设置图标大小和内边距
            int padding = (int) getResources().getDimension(R.dimen.recording_transfer_account_icon_padding);

            // 应用内边距
            iconView.setPadding(padding, padding, padding, padding);

            // 设置账户余额（添加到按钮文本后面）
            String balanceText = String.format(Locale.CHINA,"¥%.2f", account.getAccountBalance());
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setText(balanceText);
        }
    }

    /**
     * 交换转入转出账户
     */
    private void swapAccounts() {
        // 临时保存账户
        Account tempFromAccount = fromAccount;
        Account tempToAccount = toAccount;

        // 重置账户引用
        fromAccount = null;
        toAccount = null;

        // 交换账户
        if (tempFromAccount != null) {
            setToAccount(tempFromAccount);
        } else {
            // 如果转出账户为空，保持转入按钮原样
            recordingTransferViewBinding.tvRecordingPageTransferToAccountName.setText(R.string.recording_transfer_请输入转入账户);
            recordingTransferViewBinding.tvRecordingPageTransferToAccountBalance.setText("");
            recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon.setImageDrawable(null);
            recordingTransferViewBinding.ivRecordingPageTransferToAccountIcon.setVisibility(View.GONE);
        }

        if (tempToAccount != null) {
            setFromAccount(tempToAccount);
        } else {
            // 如果转入账户为空，保持转出按钮原样
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountName.setText(R.string.recording_transfer_请输入转出账户);
            recordingTransferViewBinding.tvRecordingPageTransferFromAccountBalance.setText("");
            recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon.setImageDrawable(null);
            recordingTransferViewBinding.ivRecordingPageTransferFromAccountIcon.setVisibility(View.GONE);
        }

        // 更新转账类型
        updateTransferType();
    }

    /**
     * 根据账户类型更新转账类型
     * - 当转出账户为信用卡或转入账户为现金时，设为"提现"
     * - 当转入账户为信用卡时，设为"还款"
     * - 其他情况为普通"转账"
     */
    private void updateTransferType() {
        if (fromAccount == null || toAccount == null) {
            return;
        }

        // 判断转账类型
        if (fromAccount.getAccountTypeDebitCredit() == 0) {
            // 转出账户是信用卡，设为提现
            transferType = "提现";
        } else if (toAccount.getAccountTypeDebitCredit() == 0) {
            // 转入账户是信用卡，设为还款
            transferType = "还款";
        } else if (toAccount.getAccountTypeName() != null &&
                toAccount.getAccountTypeName().contains("现金")) {
            // 转入账户是现金，设为提现
            transferType = "提现";
        } else {
            // 其他情况为普通转账
            transferType = "转账";
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        recordingTransferViewBinding = null;

        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown();
        }
    }
}
