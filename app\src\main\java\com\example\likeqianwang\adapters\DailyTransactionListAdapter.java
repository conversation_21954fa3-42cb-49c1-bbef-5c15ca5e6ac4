package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.ReceiptViewModel;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class DailyTransactionListAdapter extends RecyclerView.Adapter<DailyTransactionListAdapter.DailyTransactionViewHolder> {
    private final Context context;
    private final ReceiptViewModel viewModel;
    private List<String> dateKeys;
    private Map<String, List<Transactions>> groupedTransactions;
    private final AppDatabase database;
    private final ExecutorService executorService;
    private final DecimalFormat amountFormatter;
    private final SimpleDateFormat dateKeyFormatter;

    // 点击事件监听器
    private DailyTransactionDetailAdapter.OnTransactionClickListener onTransactionClickListener;

    public DailyTransactionListAdapter(Context context, ReceiptViewModel viewModel) {
        this.context = context;
        this.viewModel = viewModel;
        this.dateKeys = new ArrayList<>();
        this.database = AppDatabase.getInstance(context);
        this.executorService = Executors.newSingleThreadExecutor();
        this.amountFormatter = new DecimalFormat("#,##0.00");
        this.dateKeyFormatter = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    }

    @NonNull
    @Override
    public DailyTransactionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.style_daily_in_out_list_item_view, parent, false);
        return new DailyTransactionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DailyTransactionViewHolder holder, int position) {
        String dateKey = dateKeys.get(position);
        List<Transactions> dayTransactions = groupedTransactions.get(dateKey);
        holder.bind(dateKey, dayTransactions);
    }

    @Override
    public int getItemCount() {
        return dateKeys.size();
    }

    /**
     * 更新数据
     */
    public void updateData(Map<String, List<Transactions>> newGroupedTransactions) {
        this.groupedTransactions = newGroupedTransactions;
        this.dateKeys = new ArrayList<>(newGroupedTransactions.keySet());

        // 按日期排序（最新的在前面）
        dateKeys.sort((date1, date2) -> date2.compareTo(date1));

        notifyDataSetChanged();
    }

    /**
     * 设置交易点击监听器
     */
    public void setOnTransactionClickListener(DailyTransactionDetailAdapter.OnTransactionClickListener listener) {
        this.onTransactionClickListener = listener;
    }

    class DailyTransactionViewHolder extends RecyclerView.ViewHolder {
        private final TextView dateTextView;
        private final TextView statsTextView;
        private final RecyclerView detailRecyclerView;
        private DailyTransactionDetailAdapter detailAdapter;

        public DailyTransactionViewHolder(@NonNull View itemView) {
            super(itemView);
            dateTextView = itemView.findViewById(R.id.receipt_Daily_InOut_date);
            statsTextView = itemView.findViewById(R.id.receipt_Daily_InOut_stats);
            detailRecyclerView = itemView.findViewById(R.id.receipt_Daily_InOut_detail);

            // 设置详情RecyclerView
            detailRecyclerView.setLayoutManager(new LinearLayoutManager(context));
            detailRecyclerView.setNestedScrollingEnabled(false);
        }

        public void bind(String dateKey, List<Transactions> dayTransactions) {
            // 设置日期显示
            String displayDate = viewModel.formatDateForDisplay(dateKey);
            dateTextView.setText(displayDate);

            // 设置详情适配器
            if (detailAdapter == null) {
                detailAdapter = new DailyTransactionDetailAdapter(context, dayTransactions);
                detailAdapter.setOnTransactionClickListener(onTransactionClickListener);
                detailRecyclerView.setAdapter(detailAdapter);
            } else {
                detailAdapter.updateTransactions(dayTransactions);
            }

            // 异步计算并显示统计信息
            executorService.execute(() -> {
                try {
                    // 解析日期
                    Date date = dateKeyFormatter.parse(dateKey);
                    if (date != null) {
                        // 获取当日统计
                        TransactionDao.TransactionSummary summary = database.transactionDao()
                                .getDailyTransactionSummary(date);

                        // 在主线程更新UI
                        itemView.post(() -> updateStatsUI(summary));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    // 发生错误时计算本地统计
                    itemView.post(() -> updateStatsUIFromLocal(dayTransactions));
                }
            });
        }

        private void updateStatsUI(TransactionDao.TransactionSummary summary) {
            BigDecimal income = summary.totalIncome != null ? summary.totalIncome : BigDecimal.ZERO;
            BigDecimal expense = summary.totalExpense != null ? summary.totalExpense : BigDecimal.ZERO;

            String statsText = String.format(Locale.getDefault(),
                    "收入：¥%s 支出：¥%s",
                    amountFormatter.format(income),
                    amountFormatter.format(expense));

            statsTextView.setText(statsText);
        }

        private void updateStatsUIFromLocal(List<Transactions> dayTransactions) {
            BigDecimal totalIncome = BigDecimal.ZERO;
            BigDecimal totalExpense = BigDecimal.ZERO;

            if (dayTransactions != null) {
                for (Transactions transaction : dayTransactions) {
                    if (transaction.isIncludeInStats()) {
                        switch (transaction.getType()) {
                            case "INCOME":
                                totalIncome = totalIncome.add(transaction.getAmount());
                                break;
                            case "EXPENSE":
                                totalExpense = totalExpense.add(transaction.getAmount());
                                break;
                            // TRANSFER 不计入收支统计
                        }
                    }
                }
            }

            String statsText = String.format(Locale.getDefault(),
                    "收入：¥%s 支出：¥%s",
                    amountFormatter.format(totalIncome),
                    amountFormatter.format(totalExpense));

            statsTextView.setText(statsText);
        }
    }

}
