package com.example.likeqianwang.Dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.TransactionTagCrossRef;

@Dao
public interface TransactionTagCrossRefDao {
    @Insert
    void insert(TransactionTagCrossRef join);

    @Update
    void update(TransactionTagCrossRef join);

    @Delete
    void delete(TransactionTagCrossRef join);

    @Query("DELETE FROM transaction_tag_cross_ref WHERE transactionId = :transactionId")
    void deleteTagsForRecord(int transactionId);
}
