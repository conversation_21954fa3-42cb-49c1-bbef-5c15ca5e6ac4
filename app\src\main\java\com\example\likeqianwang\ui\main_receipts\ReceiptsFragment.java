package com.example.likeqianwang.ui.main_receipts;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.example.likeqianwang.R;
import com.example.likeqianwang.RecordingPageActivity;
import com.example.likeqianwang.adapters.InOut_Budget_ViewPager_adapter;
import com.example.likeqianwang.databinding.FragmentReceiptBinding;
import com.example.likeqianwang.widgets.Widget_Budget;
import com.example.likeqianwang.widgets.Widget_InAndOut;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.List;

public class ReceiptsFragment extends Fragment implements View.OnClickListener {

    private FragmentReceiptBinding binding;
    private TabLayout InOut_Budget_tabLayout;
    private ViewPager2 InOut_Budget_viewPager;
    private final int[] InOut_Budget_tabIcons = {R.drawable.widget_icon_dot_combine, R.drawable.widget_icon_dot_combine};

    List<Fragment> InOut_Budget_fragments = new ArrayList<>();

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {

        binding = FragmentReceiptBinding.inflate(inflater, container, false);
        View root = binding.getRoot();

        InOut_Budget_tabLayout = binding.receiptInOutBudgetWidgetTabs;
        InOut_Budget_viewPager = binding.receiptInOutBudgetWidget;

        InOut_Budget_fragments.add(new Widget_InAndOut());
        InOut_Budget_fragments.add(new Widget_Budget());

        InOut_Budget_ViewPager_adapter inOut_budget_viewPager_adapter = new InOut_Budget_ViewPager_adapter(getChildFragmentManager(), getLifecycle(), InOut_Budget_fragments);
        InOut_Budget_viewPager.setAdapter(inOut_budget_viewPager_adapter);
        new TabLayoutMediator(InOut_Budget_tabLayout, InOut_Budget_viewPager, new TabLayoutMediator.TabConfigurationStrategy() {
            @Override
            public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                tab.setIcon(InOut_Budget_tabIcons[position]);
            }
        }).attach();

        ImageButton add_record = binding.buttonAdd;
        ImageView receipt_setting = binding.receiptSetting;
        add_record.setTag("add_record");
        receipt_setting.setTag("receipt_setting");
        add_record.setOnClickListener(this);
        receipt_setting.setOnClickListener(this);

        return root;

    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onClick(View v) {
        String receipt_tag = (String) v.getTag();
        switch (receipt_tag) {
            case "add_record":
                Intent start_add_record = new Intent(getActivity(), RecordingPageActivity.class);
                startActivity(start_add_record);
            case "receipt_setting":
                break;
        }
    }
}