package com.example.likeqianwang.Database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Dao.AccountDao_Impl;
import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionCategoryDao_Impl;
import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Dao.TransactionDao_Impl;
import com.example.likeqianwang.Dao.TransactionSubcategoryDao;
import com.example.likeqianwang.Dao.TransactionSubcategoryDao_Impl;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao_Impl;
import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Dao.TransactionTagDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile AccountDao _accountDao;

  private volatile TransactionDao _transactionDao;

  private volatile TransactionCategoryDao _transactionCategoryDao;

  private volatile TransactionSubcategoryDao _transactionSubcategoryDao;

  private volatile TransactionTagDao _transactionTagDao;

  private volatile TransactionTagCrossRefDao _transactionTagCrossRefDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(3) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `transaction_accounts` (`accountId` TEXT NOT NULL, `accountName` TEXT, `accountTypeId` TEXT, `accountTypeName` TEXT, `accountTypeIcon` INTEGER NOT NULL, `accountTypeCategoryId` TEXT, `accountTypeDebitCredit` INTEGER NOT NULL, `bankId` TEXT, `bankName` TEXT, `bankIcon` INTEGER NOT NULL, `accountBalance` REAL NOT NULL, `totalCredit` REAL NOT NULL, `statementDate` INTEGER NOT NULL, `dueDate` INTEGER NOT NULL, `dueDateInCurrentPeriod` INTEGER NOT NULL, `currencySymbol` TEXT, `includeInAsset` INTEGER NOT NULL, `accountRemark` TEXT, `createTime` INTEGER, `updateTime` INTEGER, PRIMARY KEY(`accountId`))");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_transaction_accounts_accountName` ON `transaction_accounts` (`accountName`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `transactions` (`transactionId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT, `transactionDate` INTEGER, `categoryId` INTEGER NOT NULL, `amount` TEXT, `currencySymbol` TEXT, `fromAccountId` TEXT, `toAccountId` TEXT, `include_in_stats` INTEGER NOT NULL, `include_in_budget` INTEGER NOT NULL, `remark` TEXT, FOREIGN KEY(`categoryId`) REFERENCES `transaction_categories`(`categoryId`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`fromAccountId`) REFERENCES `transaction_accounts`(`accountId`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transactions_transactionDate` ON `transactions` (`transactionDate`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transactions_type` ON `transactions` (`type`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transactions_categoryId` ON `transactions` (`categoryId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transactions_fromAccountId` ON `transactions` (`fromAccountId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transactions_toAccountId` ON `transactions` (`toAccountId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `transaction_categories` (`categoryId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `categoryName` TEXT, `categoryIcon` INTEGER NOT NULL, `categoryType` INTEGER NOT NULL, `hasSubCategories` INTEGER NOT NULL, `orderIndex` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `transaction_subcategories` (`subcategoryId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `subcategoryName` TEXT, `subcategoryIcon` INTEGER NOT NULL, `parentCategoryId` INTEGER NOT NULL, `orderIndex` INTEGER NOT NULL, FOREIGN KEY(`parentCategoryId`) REFERENCES `transaction_categories`(`categoryId`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transaction_subcategories_parentCategoryId` ON `transaction_subcategories` (`parentCategoryId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `transaction_tags` (`tagId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `tag_name` TEXT, `tag_color` TEXT, `tag_category` TEXT, `order_index` INTEGER NOT NULL, `is_selected` INTEGER NOT NULL)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_transaction_tags_tag_name` ON `transaction_tags` (`tag_name`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `transaction_tag_cross_ref` (`transactionId` INTEGER NOT NULL, `tagId` INTEGER NOT NULL, PRIMARY KEY(`transactionId`, `tagId`), FOREIGN KEY(`transactionId`) REFERENCES `transactions`(`transactionId`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`tagId`) REFERENCES `transaction_tags`(`tagId`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transaction_tag_cross_ref_transactionId` ON `transaction_tag_cross_ref` (`transactionId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_transaction_tag_cross_ref_tagId` ON `transaction_tag_cross_ref` (`tagId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '7e3a40a76548c3613b484f75e30f3ae9')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `transaction_accounts`");
        db.execSQL("DROP TABLE IF EXISTS `transactions`");
        db.execSQL("DROP TABLE IF EXISTS `transaction_categories`");
        db.execSQL("DROP TABLE IF EXISTS `transaction_subcategories`");
        db.execSQL("DROP TABLE IF EXISTS `transaction_tags`");
        db.execSQL("DROP TABLE IF EXISTS `transaction_tag_cross_ref`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsTransactionAccounts = new HashMap<String, TableInfo.Column>(20);
        _columnsTransactionAccounts.put("accountId", new TableInfo.Column("accountId", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountName", new TableInfo.Column("accountName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountTypeId", new TableInfo.Column("accountTypeId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountTypeName", new TableInfo.Column("accountTypeName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountTypeIcon", new TableInfo.Column("accountTypeIcon", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountTypeCategoryId", new TableInfo.Column("accountTypeCategoryId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountTypeDebitCredit", new TableInfo.Column("accountTypeDebitCredit", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("bankId", new TableInfo.Column("bankId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("bankName", new TableInfo.Column("bankName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("bankIcon", new TableInfo.Column("bankIcon", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountBalance", new TableInfo.Column("accountBalance", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("totalCredit", new TableInfo.Column("totalCredit", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("statementDate", new TableInfo.Column("statementDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("dueDate", new TableInfo.Column("dueDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("dueDateInCurrentPeriod", new TableInfo.Column("dueDateInCurrentPeriod", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("currencySymbol", new TableInfo.Column("currencySymbol", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("includeInAsset", new TableInfo.Column("includeInAsset", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("accountRemark", new TableInfo.Column("accountRemark", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("createTime", new TableInfo.Column("createTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionAccounts.put("updateTime", new TableInfo.Column("updateTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTransactionAccounts = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTransactionAccounts = new HashSet<TableInfo.Index>(1);
        _indicesTransactionAccounts.add(new TableInfo.Index("index_transaction_accounts_accountName", true, Arrays.asList("accountName"), Arrays.asList("ASC")));
        final TableInfo _infoTransactionAccounts = new TableInfo("transaction_accounts", _columnsTransactionAccounts, _foreignKeysTransactionAccounts, _indicesTransactionAccounts);
        final TableInfo _existingTransactionAccounts = TableInfo.read(db, "transaction_accounts");
        if (!_infoTransactionAccounts.equals(_existingTransactionAccounts)) {
          return new RoomOpenHelper.ValidationResult(false, "transaction_accounts(com.example.likeqianwang.Entity.Account).\n"
                  + " Expected:\n" + _infoTransactionAccounts + "\n"
                  + " Found:\n" + _existingTransactionAccounts);
        }
        final HashMap<String, TableInfo.Column> _columnsTransactions = new HashMap<String, TableInfo.Column>(11);
        _columnsTransactions.put("transactionId", new TableInfo.Column("transactionId", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("type", new TableInfo.Column("type", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("transactionDate", new TableInfo.Column("transactionDate", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("categoryId", new TableInfo.Column("categoryId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("amount", new TableInfo.Column("amount", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("currencySymbol", new TableInfo.Column("currencySymbol", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("fromAccountId", new TableInfo.Column("fromAccountId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("toAccountId", new TableInfo.Column("toAccountId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("include_in_stats", new TableInfo.Column("include_in_stats", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("include_in_budget", new TableInfo.Column("include_in_budget", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactions.put("remark", new TableInfo.Column("remark", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTransactions = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysTransactions.add(new TableInfo.ForeignKey("transaction_categories", "CASCADE", "NO ACTION", Arrays.asList("categoryId"), Arrays.asList("categoryId")));
        _foreignKeysTransactions.add(new TableInfo.ForeignKey("transaction_accounts", "CASCADE", "NO ACTION", Arrays.asList("fromAccountId"), Arrays.asList("accountId")));
        final HashSet<TableInfo.Index> _indicesTransactions = new HashSet<TableInfo.Index>(5);
        _indicesTransactions.add(new TableInfo.Index("index_transactions_transactionDate", false, Arrays.asList("transactionDate"), Arrays.asList("ASC")));
        _indicesTransactions.add(new TableInfo.Index("index_transactions_type", false, Arrays.asList("type"), Arrays.asList("ASC")));
        _indicesTransactions.add(new TableInfo.Index("index_transactions_categoryId", false, Arrays.asList("categoryId"), Arrays.asList("ASC")));
        _indicesTransactions.add(new TableInfo.Index("index_transactions_fromAccountId", false, Arrays.asList("fromAccountId"), Arrays.asList("ASC")));
        _indicesTransactions.add(new TableInfo.Index("index_transactions_toAccountId", false, Arrays.asList("toAccountId"), Arrays.asList("ASC")));
        final TableInfo _infoTransactions = new TableInfo("transactions", _columnsTransactions, _foreignKeysTransactions, _indicesTransactions);
        final TableInfo _existingTransactions = TableInfo.read(db, "transactions");
        if (!_infoTransactions.equals(_existingTransactions)) {
          return new RoomOpenHelper.ValidationResult(false, "transactions(com.example.likeqianwang.Entity.Transactions).\n"
                  + " Expected:\n" + _infoTransactions + "\n"
                  + " Found:\n" + _existingTransactions);
        }
        final HashMap<String, TableInfo.Column> _columnsTransactionCategories = new HashMap<String, TableInfo.Column>(6);
        _columnsTransactionCategories.put("categoryId", new TableInfo.Column("categoryId", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionCategories.put("categoryName", new TableInfo.Column("categoryName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionCategories.put("categoryIcon", new TableInfo.Column("categoryIcon", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionCategories.put("categoryType", new TableInfo.Column("categoryType", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionCategories.put("hasSubCategories", new TableInfo.Column("hasSubCategories", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionCategories.put("orderIndex", new TableInfo.Column("orderIndex", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTransactionCategories = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTransactionCategories = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTransactionCategories = new TableInfo("transaction_categories", _columnsTransactionCategories, _foreignKeysTransactionCategories, _indicesTransactionCategories);
        final TableInfo _existingTransactionCategories = TableInfo.read(db, "transaction_categories");
        if (!_infoTransactionCategories.equals(_existingTransactionCategories)) {
          return new RoomOpenHelper.ValidationResult(false, "transaction_categories(com.example.likeqianwang.Entity.TransactionCategory).\n"
                  + " Expected:\n" + _infoTransactionCategories + "\n"
                  + " Found:\n" + _existingTransactionCategories);
        }
        final HashMap<String, TableInfo.Column> _columnsTransactionSubcategories = new HashMap<String, TableInfo.Column>(5);
        _columnsTransactionSubcategories.put("subcategoryId", new TableInfo.Column("subcategoryId", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionSubcategories.put("subcategoryName", new TableInfo.Column("subcategoryName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionSubcategories.put("subcategoryIcon", new TableInfo.Column("subcategoryIcon", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionSubcategories.put("parentCategoryId", new TableInfo.Column("parentCategoryId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionSubcategories.put("orderIndex", new TableInfo.Column("orderIndex", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTransactionSubcategories = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysTransactionSubcategories.add(new TableInfo.ForeignKey("transaction_categories", "CASCADE", "NO ACTION", Arrays.asList("parentCategoryId"), Arrays.asList("categoryId")));
        final HashSet<TableInfo.Index> _indicesTransactionSubcategories = new HashSet<TableInfo.Index>(1);
        _indicesTransactionSubcategories.add(new TableInfo.Index("index_transaction_subcategories_parentCategoryId", false, Arrays.asList("parentCategoryId"), Arrays.asList("ASC")));
        final TableInfo _infoTransactionSubcategories = new TableInfo("transaction_subcategories", _columnsTransactionSubcategories, _foreignKeysTransactionSubcategories, _indicesTransactionSubcategories);
        final TableInfo _existingTransactionSubcategories = TableInfo.read(db, "transaction_subcategories");
        if (!_infoTransactionSubcategories.equals(_existingTransactionSubcategories)) {
          return new RoomOpenHelper.ValidationResult(false, "transaction_subcategories(com.example.likeqianwang.Entity.TransactionSubcategory).\n"
                  + " Expected:\n" + _infoTransactionSubcategories + "\n"
                  + " Found:\n" + _existingTransactionSubcategories);
        }
        final HashMap<String, TableInfo.Column> _columnsTransactionTags = new HashMap<String, TableInfo.Column>(6);
        _columnsTransactionTags.put("tagId", new TableInfo.Column("tagId", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionTags.put("tag_name", new TableInfo.Column("tag_name", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionTags.put("tag_color", new TableInfo.Column("tag_color", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionTags.put("tag_category", new TableInfo.Column("tag_category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionTags.put("order_index", new TableInfo.Column("order_index", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionTags.put("is_selected", new TableInfo.Column("is_selected", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTransactionTags = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTransactionTags = new HashSet<TableInfo.Index>(1);
        _indicesTransactionTags.add(new TableInfo.Index("index_transaction_tags_tag_name", true, Arrays.asList("tag_name"), Arrays.asList("ASC")));
        final TableInfo _infoTransactionTags = new TableInfo("transaction_tags", _columnsTransactionTags, _foreignKeysTransactionTags, _indicesTransactionTags);
        final TableInfo _existingTransactionTags = TableInfo.read(db, "transaction_tags");
        if (!_infoTransactionTags.equals(_existingTransactionTags)) {
          return new RoomOpenHelper.ValidationResult(false, "transaction_tags(com.example.likeqianwang.Entity.TransactionTag).\n"
                  + " Expected:\n" + _infoTransactionTags + "\n"
                  + " Found:\n" + _existingTransactionTags);
        }
        final HashMap<String, TableInfo.Column> _columnsTransactionTagCrossRef = new HashMap<String, TableInfo.Column>(2);
        _columnsTransactionTagCrossRef.put("transactionId", new TableInfo.Column("transactionId", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTransactionTagCrossRef.put("tagId", new TableInfo.Column("tagId", "INTEGER", true, 2, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTransactionTagCrossRef = new HashSet<TableInfo.ForeignKey>(2);
        _foreignKeysTransactionTagCrossRef.add(new TableInfo.ForeignKey("transactions", "CASCADE", "NO ACTION", Arrays.asList("transactionId"), Arrays.asList("transactionId")));
        _foreignKeysTransactionTagCrossRef.add(new TableInfo.ForeignKey("transaction_tags", "CASCADE", "NO ACTION", Arrays.asList("tagId"), Arrays.asList("tagId")));
        final HashSet<TableInfo.Index> _indicesTransactionTagCrossRef = new HashSet<TableInfo.Index>(2);
        _indicesTransactionTagCrossRef.add(new TableInfo.Index("index_transaction_tag_cross_ref_transactionId", false, Arrays.asList("transactionId"), Arrays.asList("ASC")));
        _indicesTransactionTagCrossRef.add(new TableInfo.Index("index_transaction_tag_cross_ref_tagId", false, Arrays.asList("tagId"), Arrays.asList("ASC")));
        final TableInfo _infoTransactionTagCrossRef = new TableInfo("transaction_tag_cross_ref", _columnsTransactionTagCrossRef, _foreignKeysTransactionTagCrossRef, _indicesTransactionTagCrossRef);
        final TableInfo _existingTransactionTagCrossRef = TableInfo.read(db, "transaction_tag_cross_ref");
        if (!_infoTransactionTagCrossRef.equals(_existingTransactionTagCrossRef)) {
          return new RoomOpenHelper.ValidationResult(false, "transaction_tag_cross_ref(com.example.likeqianwang.Entity.TransactionTagCrossRef).\n"
                  + " Expected:\n" + _infoTransactionTagCrossRef + "\n"
                  + " Found:\n" + _existingTransactionTagCrossRef);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "7e3a40a76548c3613b484f75e30f3ae9", "3c07487332d4bfc6236fd4622ad5dd19");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "transaction_accounts","transactions","transaction_categories","transaction_subcategories","transaction_tags","transaction_tag_cross_ref");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    final boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `transaction_accounts`");
      _db.execSQL("DELETE FROM `transactions`");
      _db.execSQL("DELETE FROM `transaction_categories`");
      _db.execSQL("DELETE FROM `transaction_subcategories`");
      _db.execSQL("DELETE FROM `transaction_tags`");
      _db.execSQL("DELETE FROM `transaction_tag_cross_ref`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(AccountDao.class, AccountDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TransactionDao.class, TransactionDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TransactionCategoryDao.class, TransactionCategoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TransactionSubcategoryDao.class, TransactionSubcategoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TransactionTagDao.class, TransactionTagDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TransactionTagCrossRefDao.class, TransactionTagCrossRefDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public AccountDao accountDao() {
    if (_accountDao != null) {
      return _accountDao;
    } else {
      synchronized(this) {
        if(_accountDao == null) {
          _accountDao = new AccountDao_Impl(this);
        }
        return _accountDao;
      }
    }
  }

  @Override
  public TransactionDao transactionDao() {
    if (_transactionDao != null) {
      return _transactionDao;
    } else {
      synchronized(this) {
        if(_transactionDao == null) {
          _transactionDao = new TransactionDao_Impl(this);
        }
        return _transactionDao;
      }
    }
  }

  @Override
  public TransactionCategoryDao transactionCategoryDao() {
    if (_transactionCategoryDao != null) {
      return _transactionCategoryDao;
    } else {
      synchronized(this) {
        if(_transactionCategoryDao == null) {
          _transactionCategoryDao = new TransactionCategoryDao_Impl(this);
        }
        return _transactionCategoryDao;
      }
    }
  }

  @Override
  public TransactionSubcategoryDao transactionSubcategoryDao() {
    if (_transactionSubcategoryDao != null) {
      return _transactionSubcategoryDao;
    } else {
      synchronized(this) {
        if(_transactionSubcategoryDao == null) {
          _transactionSubcategoryDao = new TransactionSubcategoryDao_Impl(this);
        }
        return _transactionSubcategoryDao;
      }
    }
  }

  @Override
  public TransactionTagDao transactionTagDao() {
    if (_transactionTagDao != null) {
      return _transactionTagDao;
    } else {
      synchronized(this) {
        if(_transactionTagDao == null) {
          _transactionTagDao = new TransactionTagDao_Impl(this);
        }
        return _transactionTagDao;
      }
    }
  }

  @Override
  public TransactionTagCrossRefDao transactionTagCrossRefDao() {
    if (_transactionTagCrossRefDao != null) {
      return _transactionTagCrossRefDao;
    } else {
      synchronized(this) {
        if(_transactionTagCrossRefDao == null) {
          _transactionTagCrossRefDao = new TransactionTagCrossRefDao_Impl(this);
        }
        return _transactionTagCrossRefDao;
      }
    }
  }
}
