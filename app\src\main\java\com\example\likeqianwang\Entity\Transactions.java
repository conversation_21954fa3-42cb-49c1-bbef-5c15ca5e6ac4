package com.example.likeqianwang.Entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.math.BigDecimal;
import java.util.Date;

@Entity(
        tableName = "transactions",
        indices = {
                @Index(value = "transactionDate"),
                @Index(value = "type"),
                @Index(value = "categoryId"),
                @Index(value = "fromAccountId"),
                @Index(value = "toAccountId")
        },
        foreignKeys = {
                @ForeignKey(
                        entity = TransactionCategory.class,
                        parentColumns = "categoryId",
                        childColumns = "categoryId",
                        onDelete = ForeignKey.CASCADE),
                @ForeignKey(
                        entity = Account.class,
                        parentColumns = "accountId",
                        childColumns = "fromAccountId",
                        onDelete = ForeignKey.CASCADE),
                @ForeignKey(
                        entity = Account.class,
                        parentColumns = "accountId",
                        childColumns = "toAccountId",
                        onDelete = ForeignKey.CASCADE)
        }
)
public class Transactions {
    @PrimaryKey(autoGenerate = true)
    private long transactionId;

    @ColumnInfo(name = "type")
    private String type;  // INCOME, EXPENSE, TRANSFER

    @ColumnInfo(name = "transactionDate")
    private Date transactionDate;  // 日期和时间

    @ColumnInfo(name = "categoryId")
    private long categoryId;  // 收支类型

    @ColumnInfo(name = "amount")
    private BigDecimal amount;  // 金额

    @ColumnInfo(name = "currencySymbol")
    private String currencySymbol;

    @ColumnInfo(name = "fromAccountId")
    private String fromAccountId; // 收支账户 & 转账转出账户

    @ColumnInfo(name = "toAccountId")
    private String toAccountId; // 转入账户（仅转账时使用）

    @ColumnInfo(name = "include_in_stats")
    private boolean includeInStats;  // 是否计入收支

    @ColumnInfo(name = "include_in_budget")
    private boolean includeInBudget;  // 是否计入预算

    @ColumnInfo(name = "remark")
    private String remark;  // 备注

    public long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date date) {
        this.transactionDate = date;
    }

    public long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(long categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public String getFromAccountId() {
        return fromAccountId;
    }

    public void setFromAccountId(String fromAccountId) {
        this.fromAccountId = fromAccountId;
    }

    public String getToAccountId() {
        return toAccountId;
    }

    public void setToAccountId(String toAccountId) {
        this.toAccountId = toAccountId;
    }

    public boolean isIncludeInStats() {
        return includeInStats;
    }

    public void setIncludeInStats(boolean includeInStats) {
        this.includeInStats = includeInStats;
    }

    public boolean isIncludeInBudget() {
        return includeInBudget;
    }

    public void setIncludeInBudget(boolean includeInBudget) {
        this.includeInBudget = includeInBudget;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
