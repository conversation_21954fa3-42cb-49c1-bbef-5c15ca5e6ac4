package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TransactionSubcategoryDao_Impl implements TransactionSubcategoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TransactionSubcategory> __insertionAdapterOfTransactionSubcategory;

  private final EntityDeletionOrUpdateAdapter<TransactionSubcategory> __deletionAdapterOfTransactionSubcategory;

  private final EntityDeletionOrUpdateAdapter<TransactionSubcategory> __updateAdapterOfTransactionSubcategory;

  public TransactionSubcategoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTransactionSubcategory = new EntityInsertionAdapter<TransactionSubcategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `transaction_subcategories` (`subcategoryId`,`subcategoryName`,`subcategoryIcon`,`parentCategoryId`,`orderIndex`) VALUES (nullif(?, 0),?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionSubcategory entity) {
        statement.bindLong(1, entity.getSubcategoryId());
        if (entity.getSubcategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getSubcategoryName());
        }
        statement.bindLong(3, entity.getSubcategoryIcon());
        statement.bindLong(4, entity.getParentCategoryId());
        statement.bindLong(5, entity.getOrderIndex());
      }
    };
    this.__deletionAdapterOfTransactionSubcategory = new EntityDeletionOrUpdateAdapter<TransactionSubcategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `transaction_subcategories` WHERE `subcategoryId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionSubcategory entity) {
        statement.bindLong(1, entity.getSubcategoryId());
      }
    };
    this.__updateAdapterOfTransactionSubcategory = new EntityDeletionOrUpdateAdapter<TransactionSubcategory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `transaction_subcategories` SET `subcategoryId` = ?,`subcategoryName` = ?,`subcategoryIcon` = ?,`parentCategoryId` = ?,`orderIndex` = ? WHERE `subcategoryId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionSubcategory entity) {
        statement.bindLong(1, entity.getSubcategoryId());
        if (entity.getSubcategoryName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getSubcategoryName());
        }
        statement.bindLong(3, entity.getSubcategoryIcon());
        statement.bindLong(4, entity.getParentCategoryId());
        statement.bindLong(5, entity.getOrderIndex());
        statement.bindLong(6, entity.getSubcategoryId());
      }
    };
  }

  @Override
  public long insert(final TransactionSubcategory subcategory) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfTransactionSubcategory.insertAndReturnId(subcategory);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final TransactionSubcategory subcategory) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfTransactionSubcategory.handle(subcategory);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final TransactionSubcategory subcategory) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfTransactionSubcategory.handle(subcategory);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public LiveData<List<TransactionSubcategory>> getSubcategoriesByParentId(
      final long parentCategoryId) {
    final String _sql = "SELECT * FROM transaction_subcategories WHERE parentCategoryId = ? ORDER BY orderIndex ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, parentCategoryId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_subcategories"}, false, new Callable<List<TransactionSubcategory>>() {
      @Override
      @Nullable
      public List<TransactionSubcategory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryId");
          final int _cursorIndexOfSubcategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryName");
          final int _cursorIndexOfSubcategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryIcon");
          final int _cursorIndexOfParentCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "parentCategoryId");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
          final List<TransactionSubcategory> _result = new ArrayList<TransactionSubcategory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TransactionSubcategory _item;
            final String _tmpSubcategoryName;
            if (_cursor.isNull(_cursorIndexOfSubcategoryName)) {
              _tmpSubcategoryName = null;
            } else {
              _tmpSubcategoryName = _cursor.getString(_cursorIndexOfSubcategoryName);
            }
            final int _tmpSubcategoryIcon;
            _tmpSubcategoryIcon = _cursor.getInt(_cursorIndexOfSubcategoryIcon);
            final long _tmpParentCategoryId;
            _tmpParentCategoryId = _cursor.getLong(_cursorIndexOfParentCategoryId);
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            _item = new TransactionSubcategory(_tmpSubcategoryName,_tmpSubcategoryIcon,_tmpParentCategoryId,_tmpOrderIndex);
            final long _tmpSubcategoryId;
            _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
            _item.setSubcategoryId(_tmpSubcategoryId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<TransactionSubcategory> getSubcategoriesByParentIdSync(final long parentCategoryId) {
    final String _sql = "SELECT * FROM transaction_subcategories WHERE parentCategoryId = ? ORDER BY orderIndex ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, parentCategoryId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryId");
      final int _cursorIndexOfSubcategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryName");
      final int _cursorIndexOfSubcategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryIcon");
      final int _cursorIndexOfParentCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "parentCategoryId");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
      final List<TransactionSubcategory> _result = new ArrayList<TransactionSubcategory>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final TransactionSubcategory _item;
        final String _tmpSubcategoryName;
        if (_cursor.isNull(_cursorIndexOfSubcategoryName)) {
          _tmpSubcategoryName = null;
        } else {
          _tmpSubcategoryName = _cursor.getString(_cursorIndexOfSubcategoryName);
        }
        final int _tmpSubcategoryIcon;
        _tmpSubcategoryIcon = _cursor.getInt(_cursorIndexOfSubcategoryIcon);
        final long _tmpParentCategoryId;
        _tmpParentCategoryId = _cursor.getLong(_cursorIndexOfParentCategoryId);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _item = new TransactionSubcategory(_tmpSubcategoryName,_tmpSubcategoryIcon,_tmpParentCategoryId,_tmpOrderIndex);
        final long _tmpSubcategoryId;
        _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        _item.setSubcategoryId(_tmpSubcategoryId);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public TransactionSubcategory getSubcategoryById(final long subcategoryId) {
    final String _sql = "SELECT * FROM transaction_subcategories WHERE subcategoryId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, subcategoryId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfSubcategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryId");
      final int _cursorIndexOfSubcategoryName = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryName");
      final int _cursorIndexOfSubcategoryIcon = CursorUtil.getColumnIndexOrThrow(_cursor, "subcategoryIcon");
      final int _cursorIndexOfParentCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "parentCategoryId");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "orderIndex");
      final TransactionSubcategory _result;
      if (_cursor.moveToFirst()) {
        final String _tmpSubcategoryName;
        if (_cursor.isNull(_cursorIndexOfSubcategoryName)) {
          _tmpSubcategoryName = null;
        } else {
          _tmpSubcategoryName = _cursor.getString(_cursorIndexOfSubcategoryName);
        }
        final int _tmpSubcategoryIcon;
        _tmpSubcategoryIcon = _cursor.getInt(_cursorIndexOfSubcategoryIcon);
        final long _tmpParentCategoryId;
        _tmpParentCategoryId = _cursor.getLong(_cursorIndexOfParentCategoryId);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _result = new TransactionSubcategory(_tmpSubcategoryName,_tmpSubcategoryIcon,_tmpParentCategoryId,_tmpOrderIndex);
        final long _tmpSubcategoryId;
        _tmpSubcategoryId = _cursor.getLong(_cursorIndexOfSubcategoryId);
        _result.setSubcategoryId(_tmpSubcategoryId);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
