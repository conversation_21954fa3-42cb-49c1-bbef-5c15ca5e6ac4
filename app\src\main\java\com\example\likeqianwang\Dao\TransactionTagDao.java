package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.TransactionTag;

import java.util.List;

@Dao
public interface TransactionTagDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(TransactionTag transactionTag);

    @Update
    void update(TransactionTag transactionTag);

    @Delete
    void delete(TransactionTag transactionTag);

    @Query("SELECT * FROM transaction_tags ORDER BY tag_name ASC")
    LiveData<List<TransactionTag>> getAllTags();
}
