package com.example.likeqianwang;

import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.example.likeqianwang.adapters.initViewPager_adapter;
import com.example.likeqianwang.databinding.ActivityMainBinding;
import com.example.likeqianwang.databinding.StyleNavbarItemViewBinding;
import com.example.likeqianwang.ui.main_receipts.ReceiptsFragment;
import com.example.likeqianwang.ui.main_stats.StatsFragment;
import com.example.likeqianwang.ui.main_wallets.WalletsFragment;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;
    private TabLayout tabLayout;
    private ViewPager2 viewPager2;
    private final int[] tabIcons = {R.drawable.icon_receipts, R.drawable.icon_wallets, R.drawable.icon_stats};
    private final int[] tabIconsSelected = {R.drawable.icon_receipts_selected, R.drawable.icon_wallets_selected, R.drawable.icon_stats_selected};
    private final String[] tabTitles = {"账单", "钱包", "统计"};

    List<Fragment> fragments = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        ViewCompat.setOnApplyWindowInsetsListener(binding.container, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        tabLayout = binding.initViewPagerTabs;
        viewPager2 = binding.initViewPagerFragments;

        // 禁用ViewPager2的页面滑动功能
        viewPager2.setUserInputEnabled(false);

        // 绑定页面
        fragments.add(new ReceiptsFragment());
        fragments.add(new WalletsFragment());
        fragments.add(new StatsFragment());

        initViewPager_adapter initViewPager_adapter = new initViewPager_adapter(getSupportFragmentManager(), getLifecycle(), fragments);
        viewPager2.setAdapter(initViewPager_adapter);
        new TabLayoutMediator(tabLayout, viewPager2, true, false, new TabLayoutMediator.TabConfigurationStrategy() {
            @Override
            public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                tab.setCustomView(getTabView(tabTitles[position], tabIcons[position], tabIconsSelected[position]));
            }
        }).attach();

    }

    //自定义导航栏图片文字
    public View getTabView(String title, int image_src, int image_src_selected) {
        StyleNavbarItemViewBinding stylenavbaritemviewbinding = StyleNavbarItemViewBinding.inflate(getLayoutInflater());
        TextView textView = stylenavbaritemviewbinding.navText;
        ColorStateList colorStateList = AppCompatResources.getColorStateList(this, R.color.main_activity_selector_selection_state);
        textView.setText(title);
        textView.setTextColor(colorStateList);
        ImageView imageView = stylenavbaritemviewbinding.navImage;
        StateListDrawable drawable = new StateListDrawable();
        Drawable normal = AppCompatResources.getDrawable(this, image_src);
        Drawable selected = AppCompatResources.getDrawable(this, image_src_selected);
        drawable.addState(new int[]{android.R.attr.state_selected}, selected);
        drawable.addState(new int[]{}, normal);
        imageView.setImageDrawable(drawable);
        return stylenavbaritemviewbinding.getRoot();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }

}