package com.example.likeqianwang.ui.tag_selection;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.TransactionTagViewModel;
import com.example.likeqianwang.adapters.TagCategoryAdapter;
import com.example.likeqianwang.ui.tag_management.TagManagementActivity;
import com.google.android.flexbox.FlexboxLayout;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TagSelectionBottomSheetDialog extends BottomSheetDialog {
    private final Context context;
    private TransactionTagViewModel viewModel;
    private TagCategoryAdapter adapter;
    private OnTagSelectionListener listener;
    
    // UI组件
    private LinearLayout llSelectedTagsContainer;
    private FlexboxLayout flexboxSelectedTags;
    private RecyclerView rvTagCategories;
    private Button btnCancel;
    private Button btnConfirm;
    private TextView tvTagManagement;
    
    private List<TransactionTag> selectedTags = new ArrayList<>();

    public TagSelectionBottomSheetDialog(Context context, ViewModelStoreOwner owner) {
        super(context);
        this.context = context;
        initViewModel(owner);
        initView();
        setupObservers();
    }

    private void initViewModel(ViewModelStoreOwner owner) {
        viewModel = new ViewModelProvider(owner).get(TransactionTagViewModel.class);
    }

    private void initView() {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_tag_selection, null);
        setContentView(view);
        
        llSelectedTagsContainer = view.findViewById(R.id.ll_selected_tags_container);
        flexboxSelectedTags = view.findViewById(R.id.flexbox_selected_tags);
        rvTagCategories = view.findViewById(R.id.rv_tag_categories);
        btnCancel = view.findViewById(R.id.btn_cancel);
        btnConfirm = view.findViewById(R.id.btn_confirm);
        tvTagManagement = view.findViewById(R.id.tv_tag_management);
        
        // 设置RecyclerView
        rvTagCategories.setLayoutManager(new LinearLayoutManager(context));
        
        // 设置点击事件
        btnCancel.setOnClickListener(v -> dismiss());
        btnConfirm.setOnClickListener(v -> {
            if (listener != null) {
                listener.onTagsSelected(new ArrayList<>(selectedTags));
            }
            dismiss();
        });
        
        tvTagManagement.setOnClickListener(v -> {
            Intent intent = new Intent(context, TagManagementActivity.class);
            context.startActivity(intent);
        });
    }

    private void setupObservers() {
        // 观察分类化的标签数据
        viewModel.getCategorizedTags().observe((ViewModelStoreOwner) context, categorizedTags -> {
            if (categorizedTags != null) {
                updateTagCategories(categorizedTags);
            }
        });
        
        // 观察选中的标签
        viewModel.getSelectedTags().observe((ViewModelStoreOwner) context, tags -> {
            if (tags != null) {
                selectedTags.clear();
                selectedTags.addAll(tags);
                updateSelectedTagsDisplay();
                if (adapter != null) {
                    adapter.updateSelectedTags(selectedTags);
                }
            }
        });
    }

    private void updateTagCategories(Map<String, List<TransactionTag>> categorizedTags) {
        List<String> categories = new ArrayList<>(categorizedTags.keySet());
        
        if (adapter == null) {
            adapter = new TagCategoryAdapter(context, categories, categorizedTags, selectedTags);
            adapter.setOnTagClickListener(tag -> {
                viewModel.toggleTagSelection(tag);
            });
            rvTagCategories.setAdapter(adapter);
        } else {
            // 更新适配器数据
            adapter.notifyDataSetChanged();
        }
    }

    private void updateSelectedTagsDisplay() {
        if (selectedTags.isEmpty()) {
            llSelectedTagsContainer.setVisibility(View.GONE);
        } else {
            llSelectedTagsContainer.setVisibility(View.VISIBLE);
            flexboxSelectedTags.removeAllViews();
            
            for (TransactionTag tag : selectedTags) {
                View tagView = createSelectedTagView(tag);
                flexboxSelectedTags.addView(tagView);
            }
        }
    }

    private View createSelectedTagView(TransactionTag tag) {
        TextView tagView = new TextView(context);
        tagView.setText(tag.getTagName());
        tagView.setTextSize(12);
        tagView.setTextColor(Color.WHITE);
        tagView.setBackgroundResource(R.drawable.widget_tag_item_bg);
        tagView.setBackgroundTintList(context.getColorStateList(R.color.ChaHuaHong));
        tagView.setPadding(16, 8, 16, 8);
        
        FlexboxLayout.LayoutParams params = new FlexboxLayout.LayoutParams(
                FlexboxLayout.LayoutParams.WRAP_CONTENT,
                FlexboxLayout.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(8, 4, 8, 4);
        tagView.setLayoutParams(params);
        
        return tagView;
    }

    public void setSelectedTags(List<TransactionTag> tags) {
        viewModel.setSelectedTags(tags);
    }

    public void setOnTagSelectionListener(OnTagSelectionListener listener) {
        this.listener = listener;
    }

    public interface OnTagSelectionListener {
        void onTagsSelected(List<TransactionTag> selectedTags);
    }
}
