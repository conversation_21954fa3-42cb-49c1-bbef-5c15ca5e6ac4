package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Repository.TransactionTagRepository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TransactionTagViewModel extends AndroidViewModel {
    private final TransactionTagRepository repository;
    private final MutableLiveData<List<TransactionTag>> selectedTags = new MutableLiveData<>(new ArrayList<>());
    private final MutableLiveData<Map<String, List<TransactionTag>>> categorizedTags = new MutableLiveData<>();
    private final MutableLiveData<String> operationStatus = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);

    public TransactionTagViewModel(@NonNull Application application) {
        super(application);
        repository = new TransactionTagRepository(application);
        loadTagsWithCategories();
    }

    // 获取所有标签
    public LiveData<List<TransactionTag>> getAllTags() {
        return repository.getAllTags();
    }

    // 获取所有分类
    public LiveData<List<String>> getAllCategories() {
        return repository.getAllCategories();
    }

    // 根据分类获取标签
    public LiveData<List<TransactionTag>> getTagsByCategory(String category) {
        return repository.getTagsByCategory(category);
    }

    // 获取已选择的标签
    public LiveData<List<TransactionTag>> getSelectedTags() {
        return selectedTags;
    }

    // 获取分类化的标签
    public LiveData<Map<String, List<TransactionTag>>> getCategorizedTags() {
        return categorizedTags;
    }

    // 获取操作状态
    public LiveData<String> getOperationStatus() {
        return operationStatus;
    }

    // 获取加载状态
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    // 选择/取消选择标签
    public void toggleTagSelection(TransactionTag tag) {
        List<TransactionTag> currentSelected = selectedTags.getValue();
        if (currentSelected == null) {
            currentSelected = new ArrayList<>();
        }

        boolean isSelected = false;
        for (int i = 0; i < currentSelected.size(); i++) {
            if (currentSelected.get(i).getTagId() == tag.getTagId()) {
                currentSelected.remove(i);
                isSelected = true;
                break;
            }
        }

        if (!isSelected) {
            currentSelected.add(tag);
        }

        selectedTags.setValue(currentSelected);
    }

    // 设置选中的标签
    public void setSelectedTags(List<TransactionTag> tags) {
        selectedTags.setValue(tags != null ? new ArrayList<>(tags) : new ArrayList<>());
    }

    // 清空选中的标签
    public void clearSelectedTags() {
        selectedTags.setValue(new ArrayList<>());
    }

    // 添加标签
    public void addTag(TransactionTag tag) {
        isLoading.setValue(true);
        repository.insertTag(tag, new TransactionTagRepository.OnTagOperationListener() {
            @Override
            public void onSuccess(TransactionTag tag) {
                isLoading.setValue(false);
                operationStatus.setValue("标签添加成功");
                loadTagsWithCategories();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                operationStatus.setValue("添加失败: " + error);
            }
        });
    }

    // 更新标签
    public void updateTag(TransactionTag tag) {
        isLoading.setValue(true);
        repository.updateTag(tag, new TransactionTagRepository.OnTagOperationListener() {
            @Override
            public void onSuccess(TransactionTag tag) {
                isLoading.setValue(false);
                operationStatus.setValue("标签更新成功");
                loadTagsWithCategories();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                operationStatus.setValue("更新失败: " + error);
            }
        });
    }

    // 删除标签
    public void deleteTag(TransactionTag tag) {
        isLoading.setValue(true);
        repository.deleteTag(tag, new TransactionTagRepository.OnTagOperationListener() {
            @Override
            public void onSuccess(TransactionTag tag) {
                isLoading.setValue(false);
                operationStatus.setValue("标签删除成功");
                loadTagsWithCategories();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                operationStatus.setValue("删除失败: " + error);
            }
        });
    }

    // 根据ID列表加载标签
    public void loadTagsByIds(List<Long> tagIds) {
        if (tagIds == null || tagIds.isEmpty()) {
            selectedTags.setValue(new ArrayList<>());
            return;
        }

        repository.getTagsByIds(tagIds, new TransactionTagRepository.OnTagsLoadedListener() {
            @Override
            public void onTagsLoaded(List<TransactionTag> tags) {
                selectedTags.setValue(tags);
            }

            @Override
            public void onError(String error) {
                operationStatus.setValue("加载标签失败: " + error);
            }
        });
    }

    // 加载分类化的标签
    private void loadTagsWithCategories() {
        repository.loadTagsWithCategories(new TransactionTagRepository.OnTagsWithCategoriesLoadedListener() {
            @Override
            public void onTagsWithCategoriesLoaded(List<String> categories, List<TransactionTag> tags) {
                Map<String, List<TransactionTag>> categorizedMap = new HashMap<>();

                // 按分类分组标签
                for (String category : categories) {
                    categorizedMap.put(category, new ArrayList<>());
                }

                // 添加未分类的标签
                if (!categorizedMap.containsKey("未分类")) {
                    categorizedMap.put("未分类", new ArrayList<>());
                }

                for (TransactionTag tag : tags) {
                    String category = tag.getTagCategory();
                    if (category == null || category.trim().isEmpty()) {
                        category = "未分类";
                    }

                    if (!categorizedMap.containsKey(category)) {
                        categorizedMap.put(category, new ArrayList<>());
                    }
                    categorizedMap.get(category).add(tag);
                }

                categorizedTags.setValue(categorizedMap);
            }

            @Override
            public void onError(String error) {
                operationStatus.setValue("加载标签失败: " + error);
            }
        });
    }

    // 刷新数据
    public void refresh() {
        loadTagsWithCategories();
    }
}
