package com.example.likeqianwang.Database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionSubcategoryDao;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao;
import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.Entity.TransactionTagCrossRef;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.Utils.Converters;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Database(
        entities = {
                Account.class,
                Transactions.class,
                TransactionCategory.class,
                TransactionSubcategory.class,
                TransactionTag.class,
                TransactionTagCrossRef.class
        },
        version = 1,
        exportSchema = false
)
@TypeConverters({Converters.class})
public abstract class AppDatabase extends RoomDatabase {
    private static final int NUMBER_OF_THREADS = 4;
    private static volatile AppDatabase INSTANCE;

    // 定义线程池
    public static final ExecutorService databaseWriteExecutor =
            Executors.newFixedThreadPool(NUMBER_OF_THREADS);

    public abstract AccountDao accountDao();

    public abstract TransactionDao transactionDao();

    public abstract TransactionCategoryDao transactionCategoryDao();
    public abstract TransactionSubcategoryDao transactionSubcategoryDao();

    public abstract TransactionTagDao transactionTagDao();

    public abstract TransactionTagCrossRefDao transactionTagCrossRefDao();

    public static AppDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                                    context.getApplicationContext(),
                                    AppDatabase.class,
                                    "accounting.db"
                            )
                            .fallbackToDestructiveMigration()
                            .setQueryExecutor(databaseWriteExecutor)
                            .setTransactionExecutor(databaseWriteExecutor)
                            .build();
                }
            }
        }
        return INSTANCE;
    }

    // 新的事务执行方法
    public void runTransaction(Runnable task) {
        databaseWriteExecutor.execute(() -> {
            runInTransaction(task);
        });
    }

}
