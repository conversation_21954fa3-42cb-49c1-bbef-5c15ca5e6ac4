package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.DataModel.AccountCategory;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.R;
import com.example.likeqianwang.Utils.CurrencyFormatter;
import com.example.likeqianwang.Utils.InnerDividerItemDecoration;
import com.example.likeqianwang.Utils.SwipeToDeleteCallback;
import com.example.likeqianwang.databinding.ItemAccountCategoryInWalletsBinding;

import java.util.List;

public class AccountCategoryInWallet_adapter extends RecyclerView.Adapter<AccountCategoryInWallet_adapter.CategoryViewHolder> {
    private final List<AccountCategory> categories;
    private final OnAccountClickListener accountClickListener;
    private OnCategoryBindListener categoryBindListener;

    public interface OnAccountClickListener {
        void onAccountClick(String accountId);
    }

    // 添加这个接口和成员变量
    public interface OnCategoryBindListener {
        void onCategoryBound(int position, RecyclerView nestedRecyclerView);
    }

    public interface OnCategoryExpandListener {
        void onCategoryExpand(int position, boolean isExpanded);
    }

    private OnCategoryExpandListener expandListener;

    public void setOnCategoryExpandListener(OnCategoryExpandListener listener) {
        this.expandListener = listener;
    }

    public void setOnCategoryBindListener(OnCategoryBindListener listener) {
        this.categoryBindListener = listener;
    }

    public AccountCategoryInWallet_adapter(Context context, List<AccountCategory> categories, OnAccountClickListener clickListener) {
        this.categories = categories;
        this.accountClickListener = clickListener;
        
        // 确保所有有账户的分类默认为展开状态
        for (AccountCategory category : categories) {
            // 检查分类是否有账户
            if (category.getAccounts() != null && !category.getAccounts().isEmpty()) {
                // 设置为展开状态
                category.setExpanded(true);
            }
        }
    }

    // 添加删除监听器接口
    public interface OnAccountDeleteListener {
        void onAccountDelete(Account account);
    }

    private OnAccountDeleteListener deleteListener;

    public void setOnAccountDeleteListener(OnAccountDeleteListener listener) {
        this.deleteListener = listener;
    }

    // 添加嵌套适配器提供者接口
    public interface NestedAdapterProvider {
        RecyclerView.Adapter<?> getNestedAdapter(int categoryPosition);
    }

    // 添加嵌套RecyclerView提供者接口
    public interface NestedRecyclerViewProvider {
        RecyclerView getNestedRecyclerView(int categoryPosition);
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemAccountCategoryInWalletsBinding accountCategoryInWalletsBinding = ItemAccountCategoryInWalletsBinding.inflate(LayoutInflater.from(parent.getContext()),parent,false);
        return new CategoryViewHolder(accountCategoryInWalletsBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        AccountCategory category = categories.get(position);

        // 设置分类名称和总金额
        holder.categoryName.setText(category.getName());
        holder.categoryTotal.setText(CurrencyFormatter.format(category.getTotalAmount()));

        // 获取账户列表
        List<Account> accounts = category.getAccounts();
        boolean hasAccounts = accounts != null && !accounts.isEmpty();

        // 设置内层RecyclerView
        if (hasAccounts) {
            // 添加自定义分割线
            Context context = holder.accountList.getContext();
            int dividerColor = ContextCompat.getColor(context, R.color.YinBai); // 自定义颜色
            int dividerHeight = context.getResources().getDimensionPixelSize(R.dimen.divider_height);
            int leftMargin = context.getResources().getDimensionPixelSize(R.dimen.divider_in_wallets_left_margin);
            int rightMargin = context.getResources().getDimensionPixelSize(R.dimen.divider_in_wallets_right_margin);

            // 设置账户列表适配器
            AccountItemInWallet_adapter accountAdapter = new AccountItemInWallet_adapter(
                    holder.itemView.getContext(),
                    accounts,
                    accountClickListener,
                    account -> {
                        if (deleteListener != null) {
                            deleteListener.onAccountDelete(account);
                        }
                    });

            holder.accountList.setAdapter(accountAdapter);

            // 设置布局管理器
            if (holder.accountList.getLayoutManager() == null) {
                holder.accountList.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext()));
            }

            // 移除所有现有的ItemDecoration，避免重复添加
            while (holder.accountList.getItemDecorationCount() > 0) {
                holder.accountList.removeItemDecorationAt(0);
            }

            // 设置分割线
            InnerDividerItemDecoration dividerItemDecoration = new InnerDividerItemDecoration(
                    context, dividerColor, dividerHeight, leftMargin, rightMargin);
            holder.accountList.addItemDecoration(dividerItemDecoration);

            // 添加滑动删除回调
            SwipeToDeleteCallback swipeHandler = new SwipeToDeleteCallback(
                    holder.itemView.getContext(),
                    new SwipeToDeleteCallback.SwipeActionListener() {
                        @Override
                        public void onDeleteClick(RecyclerView.ViewHolder viewHolder) {
                            int adapterPosition = viewHolder.getAdapterPosition();
                            if (adapterPosition != RecyclerView.NO_POSITION && adapterPosition < accounts.size()) {
                                Account account = accounts.get(adapterPosition);
                                if (deleteListener != null) {
                                    deleteListener.onAccountDelete(account);
                                }
                            }
                        }
                    },
                    true);

            ItemTouchHelper itemTouchHelper = new ItemTouchHelper(swipeHandler);
            itemTouchHelper.attachToRecyclerView(holder.accountList);

            // 存储嵌套适配器和RecyclerView的引用
            if (holder.getAdapterPosition() != RecyclerView.NO_POSITION) {
                int categoryPos = holder.getAdapterPosition();
                if (context instanceof NestedAdapterProvider) {
                    ((NestedAdapterProvider) context).getNestedAdapter(categoryPos);
                }
                if (context instanceof NestedRecyclerViewProvider) {
                    ((NestedRecyclerViewProvider) context).getNestedRecyclerView(categoryPos);
                }
            }

            // 设置分类展开/折叠状态
            updateCategoryExpandState(holder, category.isExpanded());

            // 设置分类点击事件
            holder.accountCategory.setOnClickListener(v -> {
                boolean newExpandedState = !category.isExpanded();
                category.setExpanded(newExpandedState);

                // 更新UI
                updateCategoryExpandState(holder, newExpandedState);

                // 如果折叠，关闭所有打开的滑动项
                if (!newExpandedState) {
                    AccountItemInWallet_adapter adapter = (AccountItemInWallet_adapter) holder.accountList.getAdapter();
                    if (adapter != null) {
                        adapter.closeOpenItem(holder.accountList);
                    }
                }

                // 通知展开/折叠监听器
                if (expandListener != null) {
                    expandListener.onCategoryExpand(position, newExpandedState);
                }
            });
        } else {
            // 没有账户，隐藏RecyclerView
            holder.accountList.setVisibility(View.GONE);
        }
    }

    // 更新分类展开状态的辅助方法
    private void updateCategoryExpandState(CategoryViewHolder holder, boolean isExpanded) {
        holder.accountList.setVisibility(isExpanded ? View.VISIBLE : View.GONE);
        // 设置展开/折叠图标
        holder.expandIcon.setImageResource(isExpanded ?
                R.drawable.icon_arrow_down :
                R.drawable.icon_arrow_right);
    }

    // 添加一个公共方法来处理账户删除后的UI更新
    public void notifyAccountRemoved(String categoryId, int accountPosition) {
        // 查找对应的分类
        for (int i = 0; i < categories.size(); i++) {
            AccountCategory category = categories.get(i);
            if (category.getId().equals(categoryId)) {
                List<Account> accounts = category.getAccounts();

                // 如果删除后账户列表为空，需要更新UI
                if (accounts.isEmpty()) {
                    category.setExpanded(false); // 自动折叠空分类
                    // 确保UI立即更新，强制重新绑定该项
                    notifyItemChanged(i);
                    // 通知外部监听器，可能需要完全隐藏此分类
                    if (expandListener != null) {
                        expandListener.onCategoryExpand(i, false);
                    }
                } else {
                    // 如果还有其他账户，只需要通知适配器账户被删除
                    RecyclerView.Adapter<?> adapter = getNestedAdapter(i);
                    if (adapter != null) {
                        adapter.notifyItemRemoved(accountPosition);
                        // 确保SwipeToDelete功能重新应用到剩余项
                        if (categoryBindListener != null) {
                            RecyclerView nestedRecyclerView = null;
                            // 尝试获取RecyclerView实例
                            if (categoryBindListener instanceof NestedRecyclerViewProvider) {
                                nestedRecyclerView = ((NestedRecyclerViewProvider) categoryBindListener)
                                        .getNestedRecyclerView(i);
                            }
                            if (nestedRecyclerView != null) {
                                categoryBindListener.onCategoryBound(i, nestedRecyclerView);
                            }
                        }
                    }
                }
                break;
            }
        }
    }

    // 辅助方法：获取嵌套的适配器
    private RecyclerView.Adapter<?> getNestedAdapter(int categoryPosition) {
        if (categoryBindListener instanceof NestedAdapterProvider) {
            return ((NestedAdapterProvider) categoryBindListener).getNestedAdapter(categoryPosition);
        }
        return null;
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    public void updateCategories(List<AccountCategory> newCategories) {
        // 使用自定义的 DiffCallback 计算差异
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(
                new AccountCategoryDiffCallback(categories, newCategories));

        this.categories.clear();
        this.categories.addAll(newCategories);

        // 应用计算出的差异更新
        diffResult.dispatchUpdatesTo(this);
    }

    /**
     * 用于比较两个账户分类列表差异的回调类
     */
    private static class AccountCategoryDiffCallback extends DiffUtil.Callback {
        private final List<AccountCategory> oldList;
        private final List<AccountCategory> newList;

        public AccountCategoryDiffCallback(List<AccountCategory> oldList, List<AccountCategory> newList) {
            this.oldList = oldList;
            this.newList = newList;
        }

        @Override
        public int getOldListSize() {
            return oldList.size();
        }

        @Override
        public int getNewListSize() {
            return newList.size();
        }

        @Override
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            // 使用唯一标识符比较项目是否相同
            return oldList.get(oldItemPosition).getId().equals(newList.get(newItemPosition).getId());
        }

        @Override
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            AccountCategory oldCategory = oldList.get(oldItemPosition);
            AccountCategory newCategory = newList.get(newItemPosition);

            // 比较基本信息
            if (!oldCategory.getName().equals(newCategory.getName()) ||
                    oldCategory.getTotalAmount() != newCategory.getTotalAmount() ||
                    oldCategory.isExpanded() != newCategory.isExpanded()) {
                return false;
            }

            // 比较账户列表大小
            List<Account> oldAccounts = oldCategory.getAccounts();
            List<Account> newAccounts = newCategory.getAccounts();

            if (oldAccounts.size() != newAccounts.size()) {
                return false;
            }

            // 简单比较账户ID列表
            for (int i = 0; i < oldAccounts.size(); i++) {
                if (!oldAccounts.get(i).getAccountId().equals(newAccounts.get(i).getAccountId())) {
                    return false;
                }
            }

            return true;
        }
    }

    public static class CategoryViewHolder extends RecyclerView.ViewHolder {
        LinearLayout accountCategory;
        TextView categoryName;
        TextView categoryTotal;
        ImageView expandIcon;
        RecyclerView accountList;

        public CategoryViewHolder(@NonNull ItemAccountCategoryInWalletsBinding accountCategoryInWalletsBinding) {
            super(accountCategoryInWalletsBinding.getRoot());
            accountCategory = accountCategoryInWalletsBinding.accountCategory;
            categoryName = accountCategoryInWalletsBinding.tvAccountCategoryName;
            categoryTotal = accountCategoryInWalletsBinding.tvAccountCategoryTotal;
            expandIcon = accountCategoryInWalletsBinding.ivAccountExpandIcon;
            accountList = accountCategoryInWalletsBinding.rvAccountList;
        }
    }
}
