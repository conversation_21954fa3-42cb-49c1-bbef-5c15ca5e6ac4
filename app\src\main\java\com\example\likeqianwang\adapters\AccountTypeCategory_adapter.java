package com.example.likeqianwang.adapters;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.DataModel.AccountTypeCategory;
import com.example.likeqianwang.DataModel.AccountTypeItem;
import com.example.likeqianwang.R;
import com.example.likeqianwang.Utils.InnerDividerItemDecoration;
import com.example.likeqianwang.databinding.ItemAccountTypeCategoryBinding;

import java.util.List;

public class AccountTypeCategory_adapter extends RecyclerView.Adapter<AccountTypeCategory_adapter.CategoryViewHolder> {

    private final List<AccountTypeCategory> categories;
    private final OnCategoryInteractionListener listener;
    
    // 缓存机制：缓存子适配器以避免重复创建
    private final SparseArray<AccountTypeItem_adapter> childAdaptersCache = new SparseArray<>();

    public interface OnCategoryInteractionListener {
        void onCategoryClick(AccountTypeCategory category, int position);
        void onItemClick(AccountTypeCategory category, AccountTypeItem item, int categoryPosition, int itemPosition);
    }

    public AccountTypeCategory_adapter(Context context, List<AccountTypeCategory> categories, OnCategoryInteractionListener listener) {
        this.categories = categories;
        this.listener = listener;
        
        // 初始化展开状态
        for (AccountTypeCategory category : categories) {
            category.setExpanded(true);
        }
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemAccountTypeCategoryBinding itemAccountTypeCategoryBinding = ItemAccountTypeCategoryBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new CategoryViewHolder(itemAccountTypeCategoryBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        AccountTypeCategory category = categories.get(position);
        holder.tvCategoryName.setText(category.getName());

        // 设置展开/折叠图标
        updateExpandIcon(holder.ivExpandIcon, category.isExpanded());

        // 设置子RecyclerView的可见性（不使用动画，因为动画会在点击时应用）
        holder.rvItems.setVisibility(category.isExpanded() ? View.VISIBLE : View.GONE);

        // 使用缓存的适配器或创建新的适配器
        AccountTypeItem_adapter itemAdapter = childAdaptersCache.get(position);
        if (itemAdapter == null) {
            itemAdapter = new AccountTypeItem_adapter(category.getItems(),
                (item, itemPosition) -> {
                    if (listener != null) {
                        listener.onItemClick(category, item, position, itemPosition);
                    }
                });
            childAdaptersCache.put(position, itemAdapter);
        }

        // 设置子RecyclerView
        if (holder.rvItems.getAdapter() == null) {
            // 添加自定义分割线
            Context context = holder.rvItems.getContext();
            int dividerColor = ContextCompat.getColor(context, R.color.YinBai); // 自定义颜色
            int dividerHeight = context.getResources().getDimensionPixelSize(R.dimen.divider_height);
            int leftMargin = context.getResources().getDimensionPixelSize(R.dimen.divider_left_margin);
            int rightMargin = context.getResources().getDimensionPixelSize(R.dimen.divider_right_margin);

            holder.rvItems.setLayoutManager(new LinearLayoutManager(holder.itemView.getContext()));
            InnerDividerItemDecoration dividerItemDecoration = new InnerDividerItemDecoration(
                    context, dividerColor, dividerHeight, leftMargin, rightMargin);
            holder.rvItems.addItemDecoration(dividerItemDecoration);
            holder.rvItems.setAdapter(itemAdapter);
        }

        // 设置点击事件
        holder.tvCategoryName.setOnClickListener(v -> toggleCategoryExpansion(holder, category, position));
        holder.ivExpandIcon.setOnClickListener(v -> toggleCategoryExpansion(holder, category, position));
    }

    private void toggleCategoryExpansion(CategoryViewHolder holder, AccountTypeCategory category, int position) {
        if (listener != null) {
            listener.onCategoryClick(category, position);
        }
        
        // 切换展开/折叠状态
        boolean isExpanded = category.isExpanded();
        category.setExpanded(!isExpanded);
        
        // 应用动画
        applyExpandCollapseAnimation(holder, !isExpanded);
    }

    private void applyExpandCollapseAnimation(CategoryViewHolder holder, boolean expand) {
        if (expand) {
            // 展开动画
            updateExpandIcon(holder.ivExpandIcon, true);
            holder.rvItems.setVisibility(View.VISIBLE);
            holder.rvItems.setAlpha(0f);
            holder.rvItems.animate()
                    .alpha(1f)
                    .setDuration(150)
                    .setListener(null);
        } else {
            // 折叠动画
            updateExpandIcon(holder.ivExpandIcon, false);
            holder.rvItems.animate()
                    .alpha(0f)
                    .setDuration(150)
                    .setListener(new AnimatorListenerAdapter() {
                        @Override
                        public void onAnimationEnd(Animator animation) {
                            holder.rvItems.setVisibility(View.GONE);
                        }
                    });
        }
    }

    private void updateExpandIcon(ImageView imageView, boolean isExpanded) {
        imageView.setImageResource(isExpanded ? 
                R.drawable.icon_arrow_down : R.drawable.icon_arrow_right);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    @Override
    public void onViewRecycled(@NonNull CategoryViewHolder holder) {
        super.onViewRecycled(holder);
        // 取消正在进行的动画
        holder.rvItems.clearAnimation();
        holder.ivExpandIcon.clearAnimation();
    }

    public static class CategoryViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvCategoryName;
        private final ImageView ivExpandIcon;
        private final RecyclerView rvItems;

        public CategoryViewHolder(@NonNull ItemAccountTypeCategoryBinding itemAccountTypeCategoryBinding) {
            super(itemAccountTypeCategoryBinding.getRoot());
            tvCategoryName = itemAccountTypeCategoryBinding.walletsAccountTypeCategoryName;
            ivExpandIcon = itemAccountTypeCategoryBinding.expandIcon;
            rvItems = itemAccountTypeCategoryBinding.walletsAccountTypeItemList;
        }
    }
}