package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.google.android.flexbox.FlexboxLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TagManagement_adapter extends RecyclerView.Adapter<TagManagement_adapter.CategoryViewHolder>{
    private final Context context;
    private final List<String> categories;
    private final Map<String, List<TransactionTag>> categorizedTags;
    private OnTagActionListener onTagActionListener;

    public TagManagement_adapter(Context context, List<String> categories,
                                 Map<String, List<TransactionTag>> categorizedTags) {
        this.context = context;
        this.categories = categories != null ? categories : new ArrayList<>();
        this.categorizedTags = categorizedTags;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_tag_management_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        String category = categories.get(position);
        holder.bind(category);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    public void setOnTagActionListener(OnTagActionListener listener) {
        this.onTagActionListener = listener;
    }

    public void updateData(List<String> newCategories, Map<String, List<TransactionTag>> newCategorizedTags) {
        categories.clear();
        if (newCategories != null) {
            categories.addAll(newCategories);
        }
        notifyDataSetChanged();
    }

    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvCategoryName;
        private final TextView tvAddTagToCategory;
        private final FlexboxLayout flexboxTags;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            tvAddTagToCategory = itemView.findViewById(R.id.tv_add_tag_to_category);
            flexboxTags = itemView.findViewById(R.id.flexbox_tags);
        }

        public void bind(String category) {
            tvCategoryName.setText(category);
            flexboxTags.removeAllViews();

            // 设置添加标签点击事件
            tvAddTagToCategory.setOnClickListener(v -> {
                if (onTagActionListener != null) {
                    onTagActionListener.onAddTagToCategory(category);
                }
            });

            List<TransactionTag> tags = categorizedTags.get(category);
            if (tags != null) {
                for (TransactionTag tag : tags) {
                    View tagView = createTagManagementView(tag);
                    flexboxTags.addView(tagView);
                }
            }
        }

        private View createTagManagementView(TransactionTag tag) {
            View tagView = LayoutInflater.from(context).inflate(R.layout.style_tag_management_item, flexboxTags, false);
            TextView tvTagName = tagView.findViewById(R.id.tv_tag_name);
            ImageView ivDeleteTag = tagView.findViewById(R.id.iv_delete_tag);

            tvTagName.setText(tag.getTagName());

            // 设置标签颜色
            if (tag.getTagColor() != null && !tag.getTagColor().isEmpty()) {
                try {
                    int color = Color.parseColor(tag.getTagColor());
                    tagView.setBackgroundColor(color);
                } catch (IllegalArgumentException e) {
                    // 如果颜色格式不正确，使用默认背景
                }
            }

            // 设置标签点击事件（编辑）
            tagView.setOnClickListener(v -> {
                if (onTagActionListener != null) {
                    onTagActionListener.onEditTag(tag);
                }
            });

            // 设置删除按钮点击事件
            ivDeleteTag.setOnClickListener(v -> {
                if (onTagActionListener != null) {
                    onTagActionListener.onDeleteTag(tag);
                }
            });

            return tagView;
        }
    }

    public interface OnTagActionListener {
        void onAddTagToCategory(String category);
        void onEditTag(TransactionTag tag);
        void onDeleteTag(TransactionTag tag);
    }

}
