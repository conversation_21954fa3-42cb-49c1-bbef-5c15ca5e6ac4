package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.google.android.flexbox.FlexboxLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TagCategoryAdapter extends RecyclerView.Adapter<TagCategoryAdapter.CategoryViewHolder> {
    private final Context context;
    private final List<String> categories;
    private final Map<String, List<TransactionTag>> categorizedTags;
    private final List<TransactionTag> selectedTags;
    private OnTagClickListener onTagClickListener;

    public TagCategoryAdapter(Context context, List<String> categories, 
                             Map<String, List<TransactionTag>> categorizedTags,
                             List<TransactionTag> selectedTags) {
        this.context = context;
        this.categories = categories != null ? categories : new ArrayList<>();
        this.categorizedTags = categorizedTags;
        this.selectedTags = selectedTags != null ? selectedTags : new ArrayList<>();
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_tag_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        String category = categories.get(position);
        holder.bind(category);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    public void setOnTagClickListener(OnTagClickListener listener) {
        this.onTagClickListener = listener;
    }

    public void updateSelectedTags(List<TransactionTag> newSelectedTags) {
        selectedTags.clear();
        if (newSelectedTags != null) {
            selectedTags.addAll(newSelectedTags);
        }
        notifyDataSetChanged();
    }

    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvCategoryName;
        private final FlexboxLayout flexboxTags;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            flexboxTags = itemView.findViewById(R.id.flexbox_tags);
        }

        public void bind(String category) {
            tvCategoryName.setText(category);
            flexboxTags.removeAllViews();

            List<TransactionTag> tags = categorizedTags.get(category);
            if (tags != null) {
                for (TransactionTag tag : tags) {
                    View tagView = createTagView(tag);
                    flexboxTags.addView(tagView);
                }
            }
        }

        private View createTagView(TransactionTag tag) {
            View tagView = LayoutInflater.from(context).inflate(R.layout.item_tag_selectable, flexboxTags, false);
            TextView tvTagName = tagView.findViewById(R.id.tv_tag_name);
            
            tvTagName.setText(tag.getTagName());
            
            // 设置标签颜色
            if (tag.getTagColor() != null && !tag.getTagColor().isEmpty()) {
                try {
                    int color = Color.parseColor(tag.getTagColor());
                    tvTagName.setBackgroundColor(color);
                } catch (IllegalArgumentException e) {
                    // 如果颜色格式不正确，使用默认背景
                }
            }
            
            // 设置选中状态
            boolean isSelected = isTagSelected(tag);
            tagView.setSelected(isSelected);
            
            // 设置点击事件
            tagView.setOnClickListener(v -> {
                if (onTagClickListener != null) {
                    onTagClickListener.onTagClick(tag);
                }
            });
            
            return tagView;
        }

        private boolean isTagSelected(TransactionTag tag) {
            for (TransactionTag selectedTag : selectedTags) {
                if (selectedTag.getTagId() == tag.getTagId()) {
                    return true;
                }
            }
            return false;
        }
    }

    public interface OnTagClickListener {
        void onTagClick(TransactionTag tag);
    }
}
