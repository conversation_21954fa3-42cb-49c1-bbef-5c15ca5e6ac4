<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_rounded_background"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择标签"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/tv_tag_management"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="管理标签"
            android:textSize="14sp"
            android:textColor="@color/ChaHuaHong"
            android:padding="8dp"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

    <!-- 已选择标签显示区域 -->
    <LinearLayout
        android:id="@+id/ll_selected_tags_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已选择："
            android:textSize="14sp"
            android:textColor="@color/grey"
            android:layout_marginBottom="8dp" />

        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/flexbox_selected_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:flexWrap="wrap"
            app:alignItems="flex_start"
            app:justifyContent="flex_start"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

    <!-- 标签列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_tag_categories"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp"
        tools:listitem="@layout/item_tag_category" />

    <!-- 底部按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="@color/grey"
            android:background="@drawable/button_outline_grey"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确定"
            android:textColor="@color/white"
            android:background="@drawable/button_primary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
