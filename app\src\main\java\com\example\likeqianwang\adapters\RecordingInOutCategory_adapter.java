package com.example.likeqianwang.adapters;

import android.content.Context;
import android.content.res.ColorStateList;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.databinding.StyleRecordingCategoryItemViewBinding;

import java.util.List;

public class RecordingInOutCategory_adapter extends RecyclerView.Adapter<RecordingInOutCategory_adapter.CategoryViewHolder> {

    private Context context;
    private final List<TransactionCategory> categories;
    private int selectedPosition = RecyclerView.NO_POSITION;
    private OnCategoryClickListener listener;
    // 记录当前选中的子类别（如果有）
    private TransactionSubcategory selectedSubcategory = null;
    private int categoryWithSelectedSubcategory = RecyclerView.NO_POSITION;

    // 添加类型常量
    public static final int TYPE_EXPENSE = 0;  // 支出
    public static final int TYPE_INCOME = 1;   // 收入
    public static final int TYPE_TRANSFER = 2; // 转账

    // 添加当前类型属性
    private int currentType = TYPE_EXPENSE;

    public interface OnCategoryClickListener {
        void onCategoryClick(TransactionCategory category, int position);
        void onManagementClick();
    }

    public RecordingInOutCategory_adapter(Context context, List<TransactionCategory> categories, int type) {
        this.context = context;
        this.categories = categories;
        this.currentType = type;
    }

    // 添加设置类型的方法
    public void setType(int type) {
        this.currentType = type;
        notifyDataSetChanged();
    }

    public void setOnCategoryClickListener(OnCategoryClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        StyleRecordingCategoryItemViewBinding recordingCategoryItemViewBinding = StyleRecordingCategoryItemViewBinding.inflate(LayoutInflater.from(context), parent, false);
        return new CategoryViewHolder(recordingCategoryItemViewBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        // 检查是否是最后一个位置（管理按钮）
        if (position == categories.size()) {
            // 设置管理按钮
            holder.categoryIcon.setImageResource(R.drawable.icon_setting_in_recording);
            holder.categoryName.setText("管理");

            // 设置管理图标为灰色
            holder.categoryIcon.setColorFilter(ContextCompat.getColor(context, R.color.defaultGrey), android.graphics.PorterDuff.Mode.SRC_IN);

            // 设置点击事件
            holder.itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onManagementClick();
                }
            });

            return;
        }

        // 正常类别项
        TransactionCategory category = categories.get(position);

        // 检查是否有选中的子类别
        if (position == categoryWithSelectedSubcategory && selectedSubcategory != null) {
            // 显示选中的子类别信息
            holder.categoryIcon.setImageResource(selectedSubcategory.getSubcategoryIcon());
            holder.categoryName.setText(selectedSubcategory.getSubcategoryName());
        } else {
            // 显示主类别信息
            holder.categoryIcon.setImageResource(category.getCategoryIcon());
            holder.categoryName.setText(category.getCategoryName());
        }

        // 设置选中状态
        boolean isSelected = position == selectedPosition;
        if (isSelected) {
            // 根据当前类型设置不同的背景和文字颜色
            int typeColor = switch (currentType) {
                case TYPE_INCOME -> R.color.WaLv;
                case TYPE_TRANSFER -> R.color.HuaQing;
                default -> R.color.ChaHuaHong;
            };

            holder.categoryIcon.setBackground(ContextCompat.getDrawable(context, R.drawable.recyclerview_item_selected_bg));
            ColorStateList typeColorStateList = ContextCompat.getColorStateList(context, typeColor);
            holder.categoryIcon.setBackgroundTintList(typeColorStateList);
            holder.categoryName.setTextColor(ContextCompat.getColor(context, typeColor));
            // 设置图标为选中颜色
            holder.categoryIcon.setColorFilter(ContextCompat.getColor(context, R.color.white), android.graphics.PorterDuff.Mode.SRC_IN);
        } else {
            holder.categoryIcon.setBackground(null);
            holder.categoryName.setTextColor(ContextCompat.getColor(context, R.color.defaultGrey));
            // 清除图标颜色过滤器
            holder.categoryIcon.setColorFilter(ContextCompat.getColor(context, R.color.defaultGrey), android.graphics.PorterDuff.Mode.SRC_IN);
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            // 获取当前最新的位置，而不是使用传入的position
            int currentPosition = holder.getAdapterPosition();
            if (currentPosition == RecyclerView.NO_POSITION) {
                return; // 位置无效，不处理
            }

            int previousSelected = selectedPosition;
            selectedPosition = currentPosition;

            // 通知适配器更新之前选中的项和当前选中的项
            if (previousSelected != RecyclerView.NO_POSITION) {
                notifyItemChanged(previousSelected);
            }
            notifyItemChanged(selectedPosition);

            // 回调监听器
            if (listener != null) {
                listener.onCategoryClick(categories.get(currentPosition), currentPosition);
            }
        });
    }

    @Override
    public int getItemCount() {
        // 额外添加一个管理按钮
        return categories.size() + 1;
    }

    // 设置选中的子类别
    public void setSelectedSubcategory(TransactionSubcategory subcategory, int categoryPosition) {
        this.selectedSubcategory = subcategory;
        this.categoryWithSelectedSubcategory = categoryPosition;

        // 确保选中的类别是包含子类别的那个
        if (selectedPosition != categoryPosition) {
            int previousSelected = selectedPosition;
            selectedPosition = categoryPosition;

            if (previousSelected != RecyclerView.NO_POSITION) {
                notifyItemChanged(previousSelected);
            }
        }

        // 更新UI
        notifyItemChanged(categoryPosition);
    }

    // 清除选中的子类别
    public void clearSelectedSubcategory() {
        if (categoryWithSelectedSubcategory != RecyclerView.NO_POSITION) {
            int position = categoryWithSelectedSubcategory;
            selectedSubcategory = null;
            categoryWithSelectedSubcategory = RecyclerView.NO_POSITION;
            notifyItemChanged(position);
        }
    }

    // 获取当前选中的位置
    public int getSelectedPosition() {
        return selectedPosition;
    }

    // 获取当前选中的子类别
    public TransactionSubcategory getSelectedSubcategory() {
        return selectedSubcategory;
    }

    // 获取当前选中的类别
    public TransactionCategory getSelectedCategory() {
        if (selectedPosition != RecyclerView.NO_POSITION && selectedPosition < categories.size()) {
            return categories.get(selectedPosition);
        }
        return null;
    }

    public static class CategoryViewHolder extends RecyclerView.ViewHolder {
        ImageView categoryIcon;
        TextView categoryName;

        public CategoryViewHolder(@NonNull StyleRecordingCategoryItemViewBinding recordingCategoryItemViewBinding) {
            super(recordingCategoryItemViewBinding.getRoot());
            categoryIcon = recordingCategoryItemViewBinding.ivRecordingCategoryItemIcon;
            categoryName = recordingCategoryItemViewBinding.tvRecordingCategoryItemName;
        }
    }
}
