package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.TransactionCategory;

import java.util.List;

@Dao
public interface TransactionCategoryDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(TransactionCategory transactionCategory);

    @Update
    void update(TransactionCategory transactionCategory);

    @Delete
    void delete(TransactionCategory transactionCategory);

    @Query("SELECT * FROM transaction_categories WHERE categoryType = :type ORDER BY orderIndex ASC")
    LiveData<List<TransactionCategory>> getCategoriesByType(int type);

    @Query("SELECT * FROM transaction_categories WHERE categoryType = :type ORDER BY orderIndex ASC")
    List<TransactionCategory> getCategoriesByTypeSync(int type);

    @Query("SELECT COUNT(*) FROM transaction_categories")
    int getCategoriesCount();

    @Query("SELECT * FROM transaction_categories WHERE categoryId = :categoryId")
    TransactionCategory getCategoryById(long categoryId);

    @Query("SELECT * FROM transaction_categories WHERE categoryId = :categoryId")
    TransactionCategory getCategoryByIdSync(long categoryId);

}
