package com.example.likeqianwang.Repository;

import androidx.room.Transaction;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao;
import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.TransactionTagCrossRef;
import com.example.likeqianwang.Entity.Transactions;

import java.math.BigDecimal;
import java.util.List;

public class TransactionRepository {
    private final AccountDao accountDao;
    private final TransactionDao transactionDao;
    private final TransactionCategoryDao categoryDao;
    private final TransactionTagDao transactionTagDao;
    private final TransactionTagCrossRefDao transactionTagCrossRefDao;

    public TransactionRepository(AppDatabase database) {
        this.transactionDao = database.transactionDao();
        this.accountDao = database.accountDao();
        this.categoryDao = database.transactionCategoryDao();
        this.transactionTagDao = database.transactionTagDao();
        this.transactionTagCrossRefDao = database.transactionTagCrossRefDao();
    }

    @Transaction
    public long addTransaction(Transactions transaction, List<TransactionTag> tags) {
        // 1. 插入交易记录
        long transactionId = transactionDao.insert(transaction);

        // 2. 更新账户余额
        updateAccountBalances(transaction);

        // 3. 保存标签关联
        if (tags != null && !tags.isEmpty()) {
            for (TransactionTag tag : tags) {
                TransactionTagCrossRef crossRef = new TransactionTagCrossRef();
                crossRef.setTransactionId(transactionId);
                crossRef.setTagId(tag.getTagId());
                transactionTagCrossRefDao.insert(crossRef);
            }
        }

        return transactionId;
    }

    private void updateAccountBalances(Transactions transaction) {
        String type = transaction.getType();
        BigDecimal amount = transaction.getAmount();

        if ("TRANSFER".equals(type)) {
            // 转账：从转出账户减少，向转入账户增加
            if (transaction.getFromAccountId() != null) {
                Account fromAccount = accountDao.getAccountByIdSync(transaction.getFromAccountId());
                if (fromAccount != null) {
                    double newBalance = fromAccount.getAccountBalance() - amount.doubleValue();
                    fromAccount.setAccountBalance(newBalance);
                    accountDao.update(fromAccount);
                }
            }

            if (transaction.getToAccountId() != null) {
                Account toAccount = accountDao.getAccountByIdSync(transaction.getToAccountId());
                if (toAccount != null) {
                    double newBalance = toAccount.getAccountBalance() + amount.doubleValue();
                    toAccount.setAccountBalance(newBalance);
                    accountDao.update(toAccount);
                }
            }
        } else {
            // 收入或支出：更新单个账户余额
            if (transaction.getFromAccountId() != null) {
                Account account = accountDao.getAccountByIdSync(transaction.getFromAccountId());
                if (account != null) {
                    double newBalance;
                    if ("INCOME".equals(type)) {
                        // 收入：增加余额
                        newBalance = account.getAccountBalance() + amount.doubleValue();
                    } else {
                        // 支出：减少余额
                        newBalance = account.getAccountBalance() - amount.doubleValue();
                    }
                    account.setAccountBalance(newBalance);
                    accountDao.update(account);
                }
            }
        }
    }
}
