package com.example.likeqianwang.Repository;

import android.util.Log;

import androidx.room.Transaction;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Dao.TransactionCategoryDao;
import com.example.likeqianwang.Dao.TransactionTagCrossRefDao;
import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.TransactionTagCrossRef;
import com.example.likeqianwang.Entity.Transactions;

import java.math.BigDecimal;
import java.util.List;

public class TransactionRepository {
    private final AccountDao accountDao;
    private final TransactionDao transactionDao;
    private final TransactionCategoryDao categoryDao;
    private final TransactionTagDao transactionTagDao;
    private final TransactionTagCrossRefDao transactionTagCrossRefDao;

    public TransactionRepository(AppDatabase database) {
        this.transactionDao = database.transactionDao();
        this.accountDao = database.accountDao();
        this.categoryDao = database.transactionCategoryDao();
        this.transactionTagDao = database.transactionTagDao();
        this.transactionTagCrossRefDao = database.transactionTagCrossRefDao();
    }

    @Transaction
    public long addTransaction(Transactions transaction, List<TransactionTag> tags) {
        // 1. 插入交易记录
        long transactionId = transactionDao.insert(transaction);

        // 2. 更新账户余额
        updateAccountBalances(transaction);

        // 3. 保存标签关联
        if (tags != null && !tags.isEmpty()) {
            for (TransactionTag tag : tags) {
                TransactionTagCrossRef crossRef = new TransactionTagCrossRef();
                crossRef.setTransactionId(transactionId);
                crossRef.setTagId(tag.getTagId());
                transactionTagCrossRefDao.insert(crossRef);
            }
        }

        return transactionId;
    }

    private void updateAccountBalances(Transactions transaction) {
        String type = transaction.getType();
        BigDecimal amount = transaction.getAmount();

        Log.d("TransactionRepository", "updateAccountBalances - Type: " + type + ", Amount: " + amount);

        if ("TRANSFER".equals(type)) {
            // 转账：从转出账户减少，向转入账户增加
            String fromAccountId = transaction.getFromAccountId();
            String toAccountId = transaction.getToAccountId();

            Log.d("TransactionRepository", "Transfer - FromAccountId: " + fromAccountId + ", ToAccountId: " + toAccountId);

            if (fromAccountId == null || fromAccountId.trim().isEmpty()) {
                throw new IllegalArgumentException("转出账户ID不能为空");
            }

            if (toAccountId == null || toAccountId.trim().isEmpty()) {
                throw new IllegalArgumentException("转入账户ID不能为空");
            }

            // 更新转出账户
            Account fromAccount = accountDao.getAccountByIdSync(fromAccountId);
            if (fromAccount == null) {
                throw new IllegalArgumentException("转出账户不存在: " + fromAccountId);
            }

            double fromOldBalance = fromAccount.getAccountBalance();
            double fromNewBalance = fromOldBalance - amount.doubleValue();

            Log.d("TransactionRepository", "FromAccount: " + fromAccount.getAccountName() +
                  ", OldBalance: " + fromOldBalance + ", NewBalance: " + fromNewBalance);

            if (fromNewBalance < 0) {
                Log.w("TransactionRepository", "Warning: FromAccount balance will be negative: " + fromNewBalance);
            }

            fromAccount.setAccountBalance(fromNewBalance);
            accountDao.update(fromAccount);
            Log.d("TransactionRepository", "FromAccount balance updated");

            // 更新转入账户
            Account toAccount = accountDao.getAccountByIdSync(toAccountId);
            if (toAccount == null) {
                throw new IllegalArgumentException("转入账户不存在: " + toAccountId);
            }

            double toOldBalance = toAccount.getAccountBalance();
            double toNewBalance = toOldBalance + amount.doubleValue();

            Log.d("TransactionRepository", "ToAccount: " + toAccount.getAccountName() +
                  ", OldBalance: " + toOldBalance + ", NewBalance: " + toNewBalance);

            toAccount.setAccountBalance(toNewBalance);
            accountDao.update(toAccount);
            Log.d("TransactionRepository", "ToAccount balance updated");

        } else {
            // 收入或支出：更新单个账户余额
            String accountId = transaction.getFromAccountId();

            if (accountId == null || accountId.trim().isEmpty()) {
                throw new IllegalArgumentException("账户ID不能为空");
            }

            Account account = accountDao.getAccountByIdSync(accountId);
            if (account == null) {
                throw new IllegalArgumentException("账户不存在: " + accountId);
            }

            double oldBalance = account.getAccountBalance();
            double newBalance;
            if ("INCOME".equals(type)) {
                // 收入：增加余额
                newBalance = oldBalance + amount.doubleValue();
            } else {
                // 支出：减少余额
                newBalance = oldBalance - amount.doubleValue();
                if (newBalance < 0) {
                    Log.w("TransactionRepository", "Warning: Account balance will be negative: " + newBalance);
                }
            }

            Log.d("TransactionRepository", "Account: " + account.getAccountName() +
                  ", Type: " + type + ", OldBalance: " + oldBalance + ", NewBalance: " + newBalance);

            account.setAccountBalance(newBalance);
            accountDao.update(account);
            Log.d("TransactionRepository", "Account balance updated");
        }
    }
}
