package com.example.likeqianwang.ui.tag_management;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.example.likeqianwang.adapters.ColorPickerAdapter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TagEditDialog extends Dialog {
    private final Context context;
    private final TransactionTag editingTag;
    private final String defaultCategory;
    private OnTagSaveListener listener;

    // UI组件
    private TextView tvDialogTitle;
    private EditText etTagName;
    private Spinner spinnerTagCategory;
    private LinearLayout llNewCategory;
    private EditText etNewCategory;
    private RecyclerView rvColorPicker;
    private Button btnCancel;
    private Button btnSave;

    // 数据
    private final List<String> predefinedCategories = Arrays.asList(
            "交通", "餐饮", "购物", "娱乐", "医疗", "教育", "住房", "通讯", "其他", "新建分类..."
    );
    private final List<String> predefinedColors = Arrays.asList(
            "#ee3f4d", "#ff6b35", "#f7931e", "#ffd23f", "#06d6a0", 
            "#118ab2", "#073b4c", "#8e44ad", "#e91e63", "#795548"
    );
    private String selectedColor = "#ee3f4d";
    private ColorPickerAdapter colorAdapter;

    public TagEditDialog(@NonNull Context context, TransactionTag tag, String defaultCategory) {
        super(context);
        this.context = context;
        this.editingTag = tag;
        this.defaultCategory = defaultCategory;
        initDialog();
    }

    private void initDialog() {
        View view = LayoutInflater.from(context).inflate(R.layout.dialog_tag_edit, null);
        setContentView(view);

        initViews(view);
        setupSpinner();
        setupColorPicker();
        setupClickListeners();
        
        if (editingTag != null) {
            populateEditData();
        } else if (defaultCategory != null) {
            setDefaultCategory();
        }
    }

    private void initViews(View view) {
        tvDialogTitle = view.findViewById(R.id.tv_dialog_title);
        etTagName = view.findViewById(R.id.et_tag_name);
        spinnerTagCategory = view.findViewById(R.id.spinner_tag_category);
        llNewCategory = view.findViewById(R.id.ll_new_category);
        etNewCategory = view.findViewById(R.id.et_new_category);
        rvColorPicker = view.findViewById(R.id.rv_color_picker);
        btnCancel = view.findViewById(R.id.btn_cancel);
        btnSave = view.findViewById(R.id.btn_save);

        // 设置标题
        tvDialogTitle.setText(editingTag != null ? "编辑标签" : "添加标签");
    }

    private void setupSpinner() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(context, 
                android.R.layout.simple_spinner_item, predefinedCategories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerTagCategory.setAdapter(adapter);

        spinnerTagCategory.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                String selected = predefinedCategories.get(position);
                if ("新建分类...".equals(selected)) {
                    llNewCategory.setVisibility(View.VISIBLE);
                } else {
                    llNewCategory.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }

    private void setupColorPicker() {
        rvColorPicker.setLayoutManager(new GridLayoutManager(context, 5));
        colorAdapter = new ColorPickerAdapter(context, predefinedColors, selectedColor);
        colorAdapter.setOnColorSelectedListener(color -> selectedColor = color);
        rvColorPicker.setAdapter(colorAdapter);
    }

    private void setupClickListeners() {
        btnCancel.setOnClickListener(v -> dismiss());
        btnSave.setOnClickListener(v -> saveTag());
    }

    private void populateEditData() {
        etTagName.setText(editingTag.getTagName());
        
        // 设置分类
        String category = editingTag.getTagCategory();
        if (category != null) {
            int index = predefinedCategories.indexOf(category);
            if (index >= 0) {
                spinnerTagCategory.setSelection(index);
            } else {
                // 自定义分类
                spinnerTagCategory.setSelection(predefinedCategories.size() - 1); // "新建分类..."
                etNewCategory.setText(category);
            }
        }
        
        // 设置颜色
        if (editingTag.getTagColor() != null) {
            selectedColor = editingTag.getTagColor();
            colorAdapter.setSelectedColor(selectedColor);
        }
    }

    private void setDefaultCategory() {
        int index = predefinedCategories.indexOf(defaultCategory);
        if (index >= 0) {
            spinnerTagCategory.setSelection(index);
        }
    }

    private void saveTag() {
        String tagName = etTagName.getText().toString().trim();
        if (TextUtils.isEmpty(tagName)) {
            Toast.makeText(context, "请输入标签名称", Toast.LENGTH_SHORT).show();
            return;
        }

        String category;
        int selectedPosition = spinnerTagCategory.getSelectedItemPosition();
        if (selectedPosition == predefinedCategories.size() - 1) {
            // 新建分类
            category = etNewCategory.getText().toString().trim();
            if (TextUtils.isEmpty(category)) {
                Toast.makeText(context, "请输入新分类名称", Toast.LENGTH_SHORT).show();
                return;
            }
        } else {
            category = predefinedCategories.get(selectedPosition);
        }

        TransactionTag tag;
        boolean isEdit = editingTag != null;
        
        if (isEdit) {
            tag = editingTag;
            tag.setTagName(tagName);
            tag.setTagCategory(category);
            tag.setTagColor(selectedColor);
        } else {
            tag = new TransactionTag(tagName, selectedColor, category);
        }

        if (listener != null) {
            listener.onTagSaved(tag, isEdit);
        }
        
        dismiss();
    }

    public void setOnTagSaveListener(OnTagSaveListener listener) {
        this.listener = listener;
    }

    public interface OnTagSaveListener {
        void onTagSaved(TransactionTag tag, boolean isEdit);
    }
}
