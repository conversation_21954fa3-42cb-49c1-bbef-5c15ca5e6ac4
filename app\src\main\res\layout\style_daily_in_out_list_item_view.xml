<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/receipt_Daily_InOut_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="02/06 星期四"
        android:textSize="12sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/receipt_Daily_InOut_stats"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="收入：¥200.00 支出：¥300.00"
        android:textSize="12sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/receipt_Daily_InOut_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/widget_tag_item_bg"
        android:backgroundTint="@color/white"
        android:scrollbars="none"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/receipt_Daily_InOut_date" />

</androidx.constraintlayout.widget.ConstraintLayout>