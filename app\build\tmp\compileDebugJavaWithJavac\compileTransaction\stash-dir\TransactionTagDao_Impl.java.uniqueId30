package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.TransactionTag;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TransactionTagDao_Impl implements TransactionTagDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<TransactionTag> __insertionAdapterOfTransactionTag;

  private final EntityDeletionOrUpdateAdapter<TransactionTag> __deletionAdapterOfTransactionTag;

  private final EntityDeletionOrUpdateAdapter<TransactionTag> __updateAdapterOfTransactionTag;

  private final SharedSQLiteStatement __preparedStmtOfDeleteById;

  public TransactionTagDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTransactionTag = new EntityInsertionAdapter<TransactionTag>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `transaction_tags` (`tagId`,`tag_name`,`tag_color`,`tag_category`,`order_index`,`is_selected`) VALUES (nullif(?, 0),?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionTag entity) {
        statement.bindLong(1, entity.getTagId());
        if (entity.getTagName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTagName());
        }
        if (entity.getTagColor() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTagColor());
        }
        if (entity.getTagCategory() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTagCategory());
        }
        statement.bindLong(5, entity.getOrderIndex());
        final int _tmp = entity.isSelected() ? 1 : 0;
        statement.bindLong(6, _tmp);
      }
    };
    this.__deletionAdapterOfTransactionTag = new EntityDeletionOrUpdateAdapter<TransactionTag>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `transaction_tags` WHERE `tagId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionTag entity) {
        statement.bindLong(1, entity.getTagId());
      }
    };
    this.__updateAdapterOfTransactionTag = new EntityDeletionOrUpdateAdapter<TransactionTag>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `transaction_tags` SET `tagId` = ?,`tag_name` = ?,`tag_color` = ?,`tag_category` = ?,`order_index` = ?,`is_selected` = ? WHERE `tagId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final TransactionTag entity) {
        statement.bindLong(1, entity.getTagId());
        if (entity.getTagName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTagName());
        }
        if (entity.getTagColor() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTagColor());
        }
        if (entity.getTagCategory() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTagCategory());
        }
        statement.bindLong(5, entity.getOrderIndex());
        final int _tmp = entity.isSelected() ? 1 : 0;
        statement.bindLong(6, _tmp);
        statement.bindLong(7, entity.getTagId());
      }
    };
    this.__preparedStmtOfDeleteById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM transaction_tags WHERE tagId = ?";
        return _query;
      }
    };
  }

  @Override
  public long insert(final TransactionTag transactionTag) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfTransactionTag.insertAndReturnId(transactionTag);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final TransactionTag transactionTag) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfTransactionTag.handle(transactionTag);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final TransactionTag transactionTag) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfTransactionTag.handle(transactionTag);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteById(final long tagId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteById.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, tagId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteById.release(_stmt);
    }
  }

  @Override
  public LiveData<List<TransactionTag>> getAllTags() {
    final String _sql = "SELECT * FROM transaction_tags ORDER BY order_index ASC, tag_name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_tags"}, false, new Callable<List<TransactionTag>>() {
      @Override
      @Nullable
      public List<TransactionTag> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
          final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
          final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
          final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
          final List<TransactionTag> _result = new ArrayList<TransactionTag>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TransactionTag _item;
            _item = new TransactionTag();
            final long _tmpTagId;
            _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
            _item.setTagId(_tmpTagId);
            final String _tmpTagName;
            if (_cursor.isNull(_cursorIndexOfTagName)) {
              _tmpTagName = null;
            } else {
              _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
            }
            _item.setTagName(_tmpTagName);
            final String _tmpTagColor;
            if (_cursor.isNull(_cursorIndexOfTagColor)) {
              _tmpTagColor = null;
            } else {
              _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
            }
            _item.setTagColor(_tmpTagColor);
            final String _tmpTagCategory;
            if (_cursor.isNull(_cursorIndexOfTagCategory)) {
              _tmpTagCategory = null;
            } else {
              _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
            }
            _item.setTagCategory(_tmpTagCategory);
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            _item.setOrderIndex(_tmpOrderIndex);
            final boolean _tmpIsSelected;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
            _tmpIsSelected = _tmp != 0;
            _item.setSelected(_tmpIsSelected);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<TransactionTag> getAllTagsSync() {
    final String _sql = "SELECT * FROM transaction_tags ORDER BY order_index ASC, tag_name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
      final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
      final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
      final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
      final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
      final List<TransactionTag> _result = new ArrayList<TransactionTag>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final TransactionTag _item;
        _item = new TransactionTag();
        final long _tmpTagId;
        _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
        _item.setTagId(_tmpTagId);
        final String _tmpTagName;
        if (_cursor.isNull(_cursorIndexOfTagName)) {
          _tmpTagName = null;
        } else {
          _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
        }
        _item.setTagName(_tmpTagName);
        final String _tmpTagColor;
        if (_cursor.isNull(_cursorIndexOfTagColor)) {
          _tmpTagColor = null;
        } else {
          _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
        }
        _item.setTagColor(_tmpTagColor);
        final String _tmpTagCategory;
        if (_cursor.isNull(_cursorIndexOfTagCategory)) {
          _tmpTagCategory = null;
        } else {
          _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
        }
        _item.setTagCategory(_tmpTagCategory);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _item.setOrderIndex(_tmpOrderIndex);
        final boolean _tmpIsSelected;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
        _tmpIsSelected = _tmp != 0;
        _item.setSelected(_tmpIsSelected);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<TransactionTag>> getTagsByCategory(final String category) {
    final String _sql = "SELECT * FROM transaction_tags WHERE tag_category = ? ORDER BY order_index ASC, tag_name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_tags"}, false, new Callable<List<TransactionTag>>() {
      @Override
      @Nullable
      public List<TransactionTag> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
          final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
          final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
          final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
          final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
          final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
          final List<TransactionTag> _result = new ArrayList<TransactionTag>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TransactionTag _item;
            _item = new TransactionTag();
            final long _tmpTagId;
            _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
            _item.setTagId(_tmpTagId);
            final String _tmpTagName;
            if (_cursor.isNull(_cursorIndexOfTagName)) {
              _tmpTagName = null;
            } else {
              _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
            }
            _item.setTagName(_tmpTagName);
            final String _tmpTagColor;
            if (_cursor.isNull(_cursorIndexOfTagColor)) {
              _tmpTagColor = null;
            } else {
              _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
            }
            _item.setTagColor(_tmpTagColor);
            final String _tmpTagCategory;
            if (_cursor.isNull(_cursorIndexOfTagCategory)) {
              _tmpTagCategory = null;
            } else {
              _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
            }
            _item.setTagCategory(_tmpTagCategory);
            final int _tmpOrderIndex;
            _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
            _item.setOrderIndex(_tmpOrderIndex);
            final boolean _tmpIsSelected;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
            _tmpIsSelected = _tmp != 0;
            _item.setSelected(_tmpIsSelected);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<TransactionTag> getTagsByCategorySync(final String category) {
    final String _sql = "SELECT * FROM transaction_tags WHERE tag_category = ? ORDER BY order_index ASC, tag_name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
      final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
      final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
      final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
      final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
      final List<TransactionTag> _result = new ArrayList<TransactionTag>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final TransactionTag _item;
        _item = new TransactionTag();
        final long _tmpTagId;
        _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
        _item.setTagId(_tmpTagId);
        final String _tmpTagName;
        if (_cursor.isNull(_cursorIndexOfTagName)) {
          _tmpTagName = null;
        } else {
          _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
        }
        _item.setTagName(_tmpTagName);
        final String _tmpTagColor;
        if (_cursor.isNull(_cursorIndexOfTagColor)) {
          _tmpTagColor = null;
        } else {
          _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
        }
        _item.setTagColor(_tmpTagColor);
        final String _tmpTagCategory;
        if (_cursor.isNull(_cursorIndexOfTagCategory)) {
          _tmpTagCategory = null;
        } else {
          _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
        }
        _item.setTagCategory(_tmpTagCategory);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _item.setOrderIndex(_tmpOrderIndex);
        final boolean _tmpIsSelected;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
        _tmpIsSelected = _tmp != 0;
        _item.setSelected(_tmpIsSelected);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<String>> getAllCategories() {
    final String _sql = "SELECT DISTINCT tag_category FROM transaction_tags WHERE tag_category IS NOT NULL ORDER BY tag_category ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"transaction_tags"}, false, new Callable<List<String>>() {
      @Override
      @Nullable
      public List<String> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final List<String> _result = new ArrayList<String>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final String _item;
            if (_cursor.isNull(0)) {
              _item = null;
            } else {
              _item = _cursor.getString(0);
            }
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<String> getAllCategoriesSync() {
    final String _sql = "SELECT DISTINCT tag_category FROM transaction_tags WHERE tag_category IS NOT NULL ORDER BY tag_category ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public TransactionTag getTagById(final long tagId) {
    final String _sql = "SELECT * FROM transaction_tags WHERE tagId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, tagId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
      final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
      final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
      final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
      final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
      final TransactionTag _result;
      if (_cursor.moveToFirst()) {
        _result = new TransactionTag();
        final long _tmpTagId;
        _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
        _result.setTagId(_tmpTagId);
        final String _tmpTagName;
        if (_cursor.isNull(_cursorIndexOfTagName)) {
          _tmpTagName = null;
        } else {
          _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
        }
        _result.setTagName(_tmpTagName);
        final String _tmpTagColor;
        if (_cursor.isNull(_cursorIndexOfTagColor)) {
          _tmpTagColor = null;
        } else {
          _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
        }
        _result.setTagColor(_tmpTagColor);
        final String _tmpTagCategory;
        if (_cursor.isNull(_cursorIndexOfTagCategory)) {
          _tmpTagCategory = null;
        } else {
          _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
        }
        _result.setTagCategory(_tmpTagCategory);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _result.setOrderIndex(_tmpOrderIndex);
        final boolean _tmpIsSelected;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
        _tmpIsSelected = _tmp != 0;
        _result.setSelected(_tmpIsSelected);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<TransactionTag> getTagsByIds(final List<Long> tagIds) {
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT * FROM transaction_tags WHERE tagId IN (");
    final int _inputSize = tagIds == null ? 1 : tagIds.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    if (tagIds == null) {
      _statement.bindNull(_argIndex);
    } else {
      for (Long _item : tagIds) {
        if (_item == null) {
          _statement.bindNull(_argIndex);
        } else {
          _statement.bindLong(_argIndex, _item);
        }
        _argIndex++;
      }
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTagId = CursorUtil.getColumnIndexOrThrow(_cursor, "tagId");
      final int _cursorIndexOfTagName = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_name");
      final int _cursorIndexOfTagColor = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_color");
      final int _cursorIndexOfTagCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "tag_category");
      final int _cursorIndexOfOrderIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "order_index");
      final int _cursorIndexOfIsSelected = CursorUtil.getColumnIndexOrThrow(_cursor, "is_selected");
      final List<TransactionTag> _result = new ArrayList<TransactionTag>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final TransactionTag _item_1;
        _item_1 = new TransactionTag();
        final long _tmpTagId;
        _tmpTagId = _cursor.getLong(_cursorIndexOfTagId);
        _item_1.setTagId(_tmpTagId);
        final String _tmpTagName;
        if (_cursor.isNull(_cursorIndexOfTagName)) {
          _tmpTagName = null;
        } else {
          _tmpTagName = _cursor.getString(_cursorIndexOfTagName);
        }
        _item_1.setTagName(_tmpTagName);
        final String _tmpTagColor;
        if (_cursor.isNull(_cursorIndexOfTagColor)) {
          _tmpTagColor = null;
        } else {
          _tmpTagColor = _cursor.getString(_cursorIndexOfTagColor);
        }
        _item_1.setTagColor(_tmpTagColor);
        final String _tmpTagCategory;
        if (_cursor.isNull(_cursorIndexOfTagCategory)) {
          _tmpTagCategory = null;
        } else {
          _tmpTagCategory = _cursor.getString(_cursorIndexOfTagCategory);
        }
        _item_1.setTagCategory(_tmpTagCategory);
        final int _tmpOrderIndex;
        _tmpOrderIndex = _cursor.getInt(_cursorIndexOfOrderIndex);
        _item_1.setOrderIndex(_tmpOrderIndex);
        final boolean _tmpIsSelected;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsSelected);
        _tmpIsSelected = _tmp != 0;
        _item_1.setSelected(_tmpIsSelected);
        _result.add(_item_1);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTagsCount() {
    final String _sql = "SELECT COUNT(*) FROM transaction_tags";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if (_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
