<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:orientation="vertical"
    android:paddingStart="15dp"
    android:paddingTop="10dp"
    android:paddingEnd="15dp"
    android:paddingBottom="25dp">

    <!-- 拖拽手柄 -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_marginBottom="16dp"
        android:layout_gravity="center_horizontal"
        android:background="#DDDDDD" />

    <!-- 标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/recording_page_account_selection_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="@string/add_new_account_返回"
            android:textSize="16sp"
            app:drawableStartCompat="@drawable/icon_arrow_back"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/recording_page_account_selection_title"
            app:layout_constraintBottom_toBottomOf="@id/recording_page_account_selection_title"/>

        <TextView
            android:id="@+id/recording_page_account_selection_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="@string/recording_选择账户"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 账户列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recording_page_account_selection_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="10dp"
        android:contentDescription="@string/recording_选择账户" />

</LinearLayout>
