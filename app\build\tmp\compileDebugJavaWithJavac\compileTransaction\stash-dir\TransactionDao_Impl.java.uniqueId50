package com.example.likeqianwang.Dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.Utils.Converters;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TransactionDao_Impl implements TransactionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Transactions> __insertionAdapterOfTransactions;

  private final EntityDeletionOrUpdateAdapter<Transactions> __deletionAdapterOfTransactions;

  private final EntityDeletionOrUpdateAdapter<Transactions> __updateAdapterOfTransactions;

  public TransactionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTransactions = new EntityInsertionAdapter<Transactions>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `transactions` (`transactionId`,`type`,`transactionDate`,`categoryId`,`amount`,`currencySymbol`,`fromAccountId`,`toAccountId`,`include_in_stats`,`include_in_budget`,`remark`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final Transactions entity) {
        statement.bindLong(1, entity.getTransactionId());
        if (entity.getType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getType());
        }
        final Long _tmp = Converters.dateToTimestamp(entity.getTransactionDate());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, _tmp);
        }
        statement.bindLong(4, entity.getCategoryId());
        final String _tmp_1 = Converters.bigDecimalToString(entity.getAmount());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_1);
        }
        if (entity.getCurrencySymbol() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getCurrencySymbol());
        }
        if (entity.getFromAccountId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFromAccountId());
        }
        if (entity.getToAccountId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getToAccountId());
        }
        final int _tmp_2 = entity.isIncludeInStats() ? 1 : 0;
        statement.bindLong(9, _tmp_2);
        final int _tmp_3 = entity.isIncludeInBudget() ? 1 : 0;
        statement.bindLong(10, _tmp_3);
        if (entity.getRemark() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getRemark());
        }
      }
    };
    this.__deletionAdapterOfTransactions = new EntityDeletionOrUpdateAdapter<Transactions>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `transactions` WHERE `transactionId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final Transactions entity) {
        statement.bindLong(1, entity.getTransactionId());
      }
    };
    this.__updateAdapterOfTransactions = new EntityDeletionOrUpdateAdapter<Transactions>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `transactions` SET `transactionId` = ?,`type` = ?,`transactionDate` = ?,`categoryId` = ?,`amount` = ?,`currencySymbol` = ?,`fromAccountId` = ?,`toAccountId` = ?,`include_in_stats` = ?,`include_in_budget` = ?,`remark` = ? WHERE `transactionId` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final Transactions entity) {
        statement.bindLong(1, entity.getTransactionId());
        if (entity.getType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getType());
        }
        final Long _tmp = Converters.dateToTimestamp(entity.getTransactionDate());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, _tmp);
        }
        statement.bindLong(4, entity.getCategoryId());
        final String _tmp_1 = Converters.bigDecimalToString(entity.getAmount());
        if (_tmp_1 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_1);
        }
        if (entity.getCurrencySymbol() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getCurrencySymbol());
        }
        if (entity.getFromAccountId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFromAccountId());
        }
        if (entity.getToAccountId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getToAccountId());
        }
        final int _tmp_2 = entity.isIncludeInStats() ? 1 : 0;
        statement.bindLong(9, _tmp_2);
        final int _tmp_3 = entity.isIncludeInBudget() ? 1 : 0;
        statement.bindLong(10, _tmp_3);
        if (entity.getRemark() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getRemark());
        }
        statement.bindLong(12, entity.getTransactionId());
      }
    };
  }

  @Override
  public long insert(final Transactions transactions) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfTransactions.insertAndReturnId(transactions);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void delete(final Transactions transactions) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __deletionAdapterOfTransactions.handle(transactions);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final Transactions transactions) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfTransactions.handle(transactions);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public Transactions getById(final long transactionId) {
    final String _sql = "SELECT * FROM Transactions WHERE transactionId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, transactionId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
      final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
      final int _cursorIndexOfTransactionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionDate");
      final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
      final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
      final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
      final int _cursorIndexOfFromAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "fromAccountId");
      final int _cursorIndexOfToAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "toAccountId");
      final int _cursorIndexOfIncludeInStats = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_stats");
      final int _cursorIndexOfIncludeInBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_budget");
      final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
      final Transactions _result;
      if (_cursor.moveToFirst()) {
        _result = new Transactions();
        final long _tmpTransactionId;
        _tmpTransactionId = _cursor.getLong(_cursorIndexOfTransactionId);
        _result.setTransactionId(_tmpTransactionId);
        final String _tmpType;
        if (_cursor.isNull(_cursorIndexOfType)) {
          _tmpType = null;
        } else {
          _tmpType = _cursor.getString(_cursorIndexOfType);
        }
        _result.setType(_tmpType);
        final Date _tmpTransactionDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfTransactionDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfTransactionDate);
        }
        _tmpTransactionDate = Converters.fromTimestampToDate(_tmp);
        _result.setTransactionDate(_tmpTransactionDate);
        final long _tmpCategoryId;
        _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
        _result.setCategoryId(_tmpCategoryId);
        final BigDecimal _tmpAmount;
        final String _tmp_1;
        if (_cursor.isNull(_cursorIndexOfAmount)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getString(_cursorIndexOfAmount);
        }
        _tmpAmount = Converters.fromString(_tmp_1);
        _result.setAmount(_tmpAmount);
        final String _tmpCurrencySymbol;
        if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
          _tmpCurrencySymbol = null;
        } else {
          _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
        }
        _result.setCurrencySymbol(_tmpCurrencySymbol);
        final String _tmpFromAccountId;
        if (_cursor.isNull(_cursorIndexOfFromAccountId)) {
          _tmpFromAccountId = null;
        } else {
          _tmpFromAccountId = _cursor.getString(_cursorIndexOfFromAccountId);
        }
        _result.setFromAccountId(_tmpFromAccountId);
        final String _tmpToAccountId;
        if (_cursor.isNull(_cursorIndexOfToAccountId)) {
          _tmpToAccountId = null;
        } else {
          _tmpToAccountId = _cursor.getString(_cursorIndexOfToAccountId);
        }
        _result.setToAccountId(_tmpToAccountId);
        final boolean _tmpIncludeInStats;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIncludeInStats);
        _tmpIncludeInStats = _tmp_2 != 0;
        _result.setIncludeInStats(_tmpIncludeInStats);
        final boolean _tmpIncludeInBudget;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfIncludeInBudget);
        _tmpIncludeInBudget = _tmp_3 != 0;
        _result.setIncludeInBudget(_tmpIncludeInBudget);
        final String _tmpRemark;
        if (_cursor.isNull(_cursorIndexOfRemark)) {
          _tmpRemark = null;
        } else {
          _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
        }
        _result.setRemark(_tmpRemark);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<Transactions>> getByDateRange(final Date startDate, final Date endDate) {
    final String _sql = "SELECT * FROM Transactions WHERE transactionDate BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = Converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = Converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"Transactions"}, false, new Callable<List<Transactions>>() {
      @Override
      @Nullable
      public List<Transactions> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfTransactionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionDate");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfFromAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "fromAccountId");
          final int _cursorIndexOfToAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "toAccountId");
          final int _cursorIndexOfIncludeInStats = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_stats");
          final int _cursorIndexOfIncludeInBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_budget");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final List<Transactions> _result = new ArrayList<Transactions>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Transactions _item;
            _item = new Transactions();
            final long _tmpTransactionId;
            _tmpTransactionId = _cursor.getLong(_cursorIndexOfTransactionId);
            _item.setTransactionId(_tmpTransactionId);
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            _item.setType(_tmpType);
            final Date _tmpTransactionDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTransactionDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfTransactionDate);
            }
            _tmpTransactionDate = Converters.fromTimestampToDate(_tmp_2);
            _item.setTransactionDate(_tmpTransactionDate);
            final long _tmpCategoryId;
            _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            _item.setCategoryId(_tmpCategoryId);
            final BigDecimal _tmpAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfAmount);
            }
            _tmpAmount = Converters.fromString(_tmp_3);
            _item.setAmount(_tmpAmount);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final String _tmpFromAccountId;
            if (_cursor.isNull(_cursorIndexOfFromAccountId)) {
              _tmpFromAccountId = null;
            } else {
              _tmpFromAccountId = _cursor.getString(_cursorIndexOfFromAccountId);
            }
            _item.setFromAccountId(_tmpFromAccountId);
            final String _tmpToAccountId;
            if (_cursor.isNull(_cursorIndexOfToAccountId)) {
              _tmpToAccountId = null;
            } else {
              _tmpToAccountId = _cursor.getString(_cursorIndexOfToAccountId);
            }
            _item.setToAccountId(_tmpToAccountId);
            final boolean _tmpIncludeInStats;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIncludeInStats);
            _tmpIncludeInStats = _tmp_4 != 0;
            _item.setIncludeInStats(_tmpIncludeInStats);
            final boolean _tmpIncludeInBudget;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIncludeInBudget);
            _tmpIncludeInBudget = _tmp_5 != 0;
            _item.setIncludeInBudget(_tmpIncludeInBudget);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _item.setRemark(_tmpRemark);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public BigDecimal getSum(final String type, final Date startDate, final Date endDate) {
    final String _sql = "SELECT SUM(amount) FROM Transactions WHERE type = ? AND transactionDate BETWEEN ? AND ? AND include_in_stats = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    _argIndex = 2;
    final Long _tmp = Converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 3;
    final Long _tmp_1 = Converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final BigDecimal _result;
      if (_cursor.moveToFirst()) {
        final String _tmp_2;
        if (_cursor.isNull(0)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getString(0);
        }
        _result = Converters.fromString(_tmp_2);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<Transactions>> getTransactionsByDateRange(final Date startDate,
      final Date endDate) {
    final String _sql = "SELECT * FROM Transactions WHERE transactionDate BETWEEN ? AND ? ORDER BY transactionDate DESC, transactionId DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final Long _tmp = Converters.dateToTimestamp(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    _argIndex = 2;
    final Long _tmp_1 = Converters.dateToTimestamp(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp_1);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"Transactions"}, false, new Callable<List<Transactions>>() {
      @Override
      @Nullable
      public List<Transactions> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfTransactionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionDate");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfFromAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "fromAccountId");
          final int _cursorIndexOfToAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "toAccountId");
          final int _cursorIndexOfIncludeInStats = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_stats");
          final int _cursorIndexOfIncludeInBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_budget");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final List<Transactions> _result = new ArrayList<Transactions>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Transactions _item;
            _item = new Transactions();
            final long _tmpTransactionId;
            _tmpTransactionId = _cursor.getLong(_cursorIndexOfTransactionId);
            _item.setTransactionId(_tmpTransactionId);
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            _item.setType(_tmpType);
            final Date _tmpTransactionDate;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfTransactionDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfTransactionDate);
            }
            _tmpTransactionDate = Converters.fromTimestampToDate(_tmp_2);
            _item.setTransactionDate(_tmpTransactionDate);
            final long _tmpCategoryId;
            _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            _item.setCategoryId(_tmpCategoryId);
            final BigDecimal _tmpAmount;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfAmount)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfAmount);
            }
            _tmpAmount = Converters.fromString(_tmp_3);
            _item.setAmount(_tmpAmount);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final String _tmpFromAccountId;
            if (_cursor.isNull(_cursorIndexOfFromAccountId)) {
              _tmpFromAccountId = null;
            } else {
              _tmpFromAccountId = _cursor.getString(_cursorIndexOfFromAccountId);
            }
            _item.setFromAccountId(_tmpFromAccountId);
            final String _tmpToAccountId;
            if (_cursor.isNull(_cursorIndexOfToAccountId)) {
              _tmpToAccountId = null;
            } else {
              _tmpToAccountId = _cursor.getString(_cursorIndexOfToAccountId);
            }
            _item.setToAccountId(_tmpToAccountId);
            final boolean _tmpIncludeInStats;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIncludeInStats);
            _tmpIncludeInStats = _tmp_4 != 0;
            _item.setIncludeInStats(_tmpIncludeInStats);
            final boolean _tmpIncludeInBudget;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIncludeInBudget);
            _tmpIncludeInBudget = _tmp_5 != 0;
            _item.setIncludeInBudget(_tmpIncludeInBudget);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _item.setRemark(_tmpRemark);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<Transactions>> getTransactionsByDate(final Date date) {
    final String _sql = "SELECT * FROM Transactions WHERE DATE(transactionDate/1000, 'unixepoch') = DATE(?/1000, 'unixepoch') ORDER BY transactionDate DESC, transactionId DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = Converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"Transactions"}, false, new Callable<List<Transactions>>() {
      @Override
      @Nullable
      public List<Transactions> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTransactionId = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionId");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfTransactionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "transactionDate");
          final int _cursorIndexOfCategoryId = CursorUtil.getColumnIndexOrThrow(_cursor, "categoryId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCurrencySymbol = CursorUtil.getColumnIndexOrThrow(_cursor, "currencySymbol");
          final int _cursorIndexOfFromAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "fromAccountId");
          final int _cursorIndexOfToAccountId = CursorUtil.getColumnIndexOrThrow(_cursor, "toAccountId");
          final int _cursorIndexOfIncludeInStats = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_stats");
          final int _cursorIndexOfIncludeInBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "include_in_budget");
          final int _cursorIndexOfRemark = CursorUtil.getColumnIndexOrThrow(_cursor, "remark");
          final List<Transactions> _result = new ArrayList<Transactions>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Transactions _item;
            _item = new Transactions();
            final long _tmpTransactionId;
            _tmpTransactionId = _cursor.getLong(_cursorIndexOfTransactionId);
            _item.setTransactionId(_tmpTransactionId);
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            _item.setType(_tmpType);
            final Date _tmpTransactionDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTransactionDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfTransactionDate);
            }
            _tmpTransactionDate = Converters.fromTimestampToDate(_tmp_1);
            _item.setTransactionDate(_tmpTransactionDate);
            final long _tmpCategoryId;
            _tmpCategoryId = _cursor.getLong(_cursorIndexOfCategoryId);
            _item.setCategoryId(_tmpCategoryId);
            final BigDecimal _tmpAmount;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAmount)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAmount);
            }
            _tmpAmount = Converters.fromString(_tmp_2);
            _item.setAmount(_tmpAmount);
            final String _tmpCurrencySymbol;
            if (_cursor.isNull(_cursorIndexOfCurrencySymbol)) {
              _tmpCurrencySymbol = null;
            } else {
              _tmpCurrencySymbol = _cursor.getString(_cursorIndexOfCurrencySymbol);
            }
            _item.setCurrencySymbol(_tmpCurrencySymbol);
            final String _tmpFromAccountId;
            if (_cursor.isNull(_cursorIndexOfFromAccountId)) {
              _tmpFromAccountId = null;
            } else {
              _tmpFromAccountId = _cursor.getString(_cursorIndexOfFromAccountId);
            }
            _item.setFromAccountId(_tmpFromAccountId);
            final String _tmpToAccountId;
            if (_cursor.isNull(_cursorIndexOfToAccountId)) {
              _tmpToAccountId = null;
            } else {
              _tmpToAccountId = _cursor.getString(_cursorIndexOfToAccountId);
            }
            _item.setToAccountId(_tmpToAccountId);
            final boolean _tmpIncludeInStats;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIncludeInStats);
            _tmpIncludeInStats = _tmp_3 != 0;
            _item.setIncludeInStats(_tmpIncludeInStats);
            final boolean _tmpIncludeInBudget;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIncludeInBudget);
            _tmpIncludeInBudget = _tmp_4 != 0;
            _item.setIncludeInBudget(_tmpIncludeInBudget);
            final String _tmpRemark;
            if (_cursor.isNull(_cursorIndexOfRemark)) {
              _tmpRemark = null;
            } else {
              _tmpRemark = _cursor.getString(_cursorIndexOfRemark);
            }
            _item.setRemark(_tmpRemark);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<TransactionDao.TransactionSummary> getMonthlyTransactionSummary(
      final String yearMonth) {
    final String _sql = "SELECT SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END) as totalIncome, SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END) as totalExpense FROM Transactions WHERE strftime('%Y-%m', transactionDate/1000, 'unixepoch') = ? AND include_in_stats = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (yearMonth == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, yearMonth);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"Transactions"}, false, new Callable<TransactionDao.TransactionSummary>() {
      @Override
      @Nullable
      public TransactionDao.TransactionSummary call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalIncome = 0;
          final int _cursorIndexOfTotalExpense = 1;
          final TransactionDao.TransactionSummary _result;
          if (_cursor.moveToFirst()) {
            _result = new TransactionDao.TransactionSummary();
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTotalIncome)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTotalIncome);
            }
            _result.totalIncome = Converters.fromString(_tmp);
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTotalExpense)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTotalExpense);
            }
            _result.totalExpense = Converters.fromString(_tmp_1);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public TransactionDao.TransactionSummary getDailyTransactionSummary(final Date date) {
    final String _sql = "SELECT SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END) as totalIncome, SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END) as totalExpense FROM Transactions WHERE DATE(transactionDate/1000, 'unixepoch') = DATE(?/1000, 'unixepoch') AND include_in_stats = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final Long _tmp = Converters.dateToTimestamp(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindLong(_argIndex, _tmp);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfTotalIncome = 0;
      final int _cursorIndexOfTotalExpense = 1;
      final TransactionDao.TransactionSummary _result;
      if (_cursor.moveToFirst()) {
        _result = new TransactionDao.TransactionSummary();
        final String _tmp_1;
        if (_cursor.isNull(_cursorIndexOfTotalIncome)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getString(_cursorIndexOfTotalIncome);
        }
        _result.totalIncome = Converters.fromString(_tmp_1);
        final String _tmp_2;
        if (_cursor.isNull(_cursorIndexOfTotalExpense)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getString(_cursorIndexOfTotalExpense);
        }
        _result.totalExpense = Converters.fromString(_tmp_2);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
