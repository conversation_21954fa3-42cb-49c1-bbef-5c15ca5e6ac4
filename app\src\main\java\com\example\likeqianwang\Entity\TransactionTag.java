package com.example.likeqianwang.Entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(tableName = "transaction_tags", indices = @Index(value = "tag_name", unique = true))
public class TransactionTag {
    @PrimaryKey(autoGenerate = true)
    private long tagId;

    @ColumnInfo(name = "tag_name")
    private String tagName;

    @ColumnInfo(name = "tag_color")
    private String tagColor;

    public long getTagId() {
        return tagId;
    }

    public void setTagId(long tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getTagColor() {
        return tagColor;
    }

    public void setTagColor(String tagColor) {
        this.tagColor = tagColor;
    }
}
